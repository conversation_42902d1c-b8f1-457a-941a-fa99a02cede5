


OLD_CHANGE_LOG = """
[1] 增强功能 2025-02-15
    - 增强了Microsoft-Copilot(BingChat)发送功能。(新功能!)
        现在可以发送提示词而无需重新加载页面。
    - 增强了Gemini和ImageFX的自动发送功能。

[2] 错误修复
    - 修复了发送提示词功能中的错误。
    - 修复了默认提示词选择中的错误。

2025-02-06
[1] 支持AI图像生成器 (新功能!)
    - 添加了对ImageFX的支持 (Google labs, 免费)
    - 右键点击在新窗口中打开ImageFX。
    - 将选中的文本发送到ImageFX。
    - 要使用ImageFX，需要生成英文提示词。
        例如：生成记忆法并为其生成图像。
    - 目前免费的ChatGPT每天可以生成3张图片。
    - 目前Gemini可以生成图像但不能生成人物。

[2] 增强保存功能 (新功能!)
    - 保存图像：右键点击将图像添加到字段。
    - 保存媒体：右键点击将mp3添加到字段。
        - AI目前无法生成mp3，所以暂时没什么用 :-/
    - 按保存将图像发送到字段。
    - 您可以选择设置要保存图像的显示大小。
        - 默认400px，实际图像大小不会改变。
    - ChatGPT和Gemini可以搜索和显示图像。
    - 这些保存功能仅支持复习器。

[3] 错误修复
    - 由于更新导致Gemini损坏，我已修复。
    - 添加了对弹出式Google身份验证的支持。
    - 也许我提高了开发技能🕺

2025-01-30
[1] 增强AI功能
    - 添加了对DeepSeek、Perplexity和Claude的支持。
    - 截至2025-01-30，DeepSeek的服务器宕机 :-/
    - Claude的Google登录不起作用，请使用邮箱登录。

2024-10-06
[1] 错误修复
    修复了导致ChatGPT自动朗读不工作的错误。

2024-09-06
[1] 错误修复
    - 修复了导致Anki在更新时崩溃的错误。
    - 禁用了插件页面的下载并放弃开发（因为该错误导致插件无法更新）。
    - 创建了新的插件页面。
    - 添加了更新前自动关闭侧边栏功能。（此插件需要在更新前完全关闭侧边栏）

[2] 功能增强
    - 添加了下拉框来选择默认提示词。

2024-09-04
[1] 功能增强
    - 添加文本到卡片：
        我开发了将侧边栏中选中的文本添加到复习器卡片的功能。(测试版)
        1. 拖拽选择AI侧边栏中的文本。
        2. 右键点击显示上下文菜单。
        3. 选择字段名称。

    - 添加了使用说明wiki：
        1. 创建了关于如何使用此插件的wiki。
        2. 在选项中添加了问号链接。

2024-08-31
[1] 功能增强
    - 添加了自动朗读功能（仅限ChatGPT）。
        - 可以选择关闭。
    - 添加了BingChat的解决方案（测试版）。
    - 在编辑器中右键点击时添加了提示词。
    - 增强了按钮按下时发送提示词的功能。
    - 添加了调整音量的选项。

[2] 错误修复
    - 修复了ChatGPT提示词中断不工作的问题。
    - 修复了Gemini提示词中断不工作的问题（测试版）。

[3] 优化
    - 移除了"input_text"选项。

2024-07-27
[1] 错误修复
    修复了更新插件时的错误。

2024-07-26
[2] 错误修复
    修复了ChatGPT自动发送功能损坏的问题。

2024-07-26 : 开始记录更新日志

2024-01-22
[1] 首次发布
"""
