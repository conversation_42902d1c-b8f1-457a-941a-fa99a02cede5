"""Wrapper for https://raw.githubusercontent.com/KhronosGroup/OpenGL-Registry/master/xml/gl.xml
Generated by tools/gengl.py.
Do not modify this file.
"""
from ctypes import (
    CFUNCTYPE,
    POINTER,
    Structure,
    c_char,
    c_double,
    c_float,
    c_int,
    c_int64,
    c_short,
    c_ubyte,
    c_uint,
    c_uint64,
    c_ushort,
)

from pyglet.gl.lib import link_GL as _link_function
from pyglet.gl.lib import c_ptrdiff_t


class struct___GLsync(Structure):  # noqa: N801
    __slots__ = [
    ]
struct___GLsync._fields_ = [
    ('_opaque_struct', c_int),
]

# END OF gl.template

# GL type definitions
GLenum = c_uint
GLboolean = c_ubyte
GLbitfield = c_uint
GLvoid = None
GLbyte = c_char
GLubyte = c_ubyte
GLshort = c_short
GLushort = c_ushort
GLint = c_int
GLuint = c_uint
GLclampx = c_uint
GLsizei = c_int
GLfloat = c_float
GLclampf = c_float
GLdouble = c_double
GLclampd = c_double
GLchar = c_char
GLintptr = c_ptrdiff_t
GLsizeiptr = c_ptrdiff_t
GLint64 = c_int64
GLuint64 = c_uint64
GLuint64EXT = c_uint64
GLsync = POINTER(struct___GLsync)
GLDEBUGPROC = CFUNCTYPE(None, GLenum, GLenum, GLuint, GLenum, GLsizei, POINTER(GLchar), POINTER(GLvoid))

# GL enumerant (token) definitions
GL_FALSE = 0
GL_POINTS = 0
GL_ZERO = 0
GL_NONE = 0
GL_NO_ERROR = 0
GL_TRUE = 1
GL_LINES = 1
GL_ONE = 1
GL_CURRENT_BIT = 1
GL_CLIENT_PIXEL_STORE_BIT = 1
GL_CONTEXT_FLAG_FORWARD_COMPATIBLE_BIT = 1
GL_MAP_READ_BIT = 1
GL_CONTEXT_CORE_PROFILE_BIT = 1
GL_SYNC_FLUSH_COMMANDS_BIT = 1
GL_VERTEX_SHADER_BIT = 1
GL_VERTEX_ATTRIB_ARRAY_BARRIER_BIT = 1
GL_LINE_LOOP = 2
GL_POINT_BIT = 2
GL_CLIENT_VERTEX_ARRAY_BIT = 2
GL_MAP_WRITE_BIT = 2
GL_CONTEXT_COMPATIBILITY_PROFILE_BIT = 2
GL_FRAGMENT_SHADER_BIT = 2
GL_ELEMENT_ARRAY_BARRIER_BIT = 2
GL_CONTEXT_FLAG_DEBUG_BIT = 2
GL_LINE_STRIP = 3
GL_TRIANGLES = 4
GL_LINE_BIT = 4
GL_MAP_INVALIDATE_RANGE_BIT = 4
GL_GEOMETRY_SHADER_BIT = 4
GL_UNIFORM_BARRIER_BIT = 4
GL_CONTEXT_FLAG_ROBUST_ACCESS_BIT = 4
GL_TRIANGLE_STRIP = 5
GL_TRIANGLE_FAN = 6
GL_QUADS = 7
GL_POLYGON_BIT = 8
GL_QUAD_STRIP = 8
GL_MAP_INVALIDATE_BUFFER_BIT = 8
GL_TESS_CONTROL_SHADER_BIT = 8
GL_TEXTURE_FETCH_BARRIER_BIT = 8
GL_CONTEXT_FLAG_NO_ERROR_BIT = 8
GL_POLYGON = 9
GL_LINES_ADJACENCY = 10
GL_LINE_STRIP_ADJACENCY = 11
GL_TRIANGLES_ADJACENCY = 12
GL_TRIANGLE_STRIP_ADJACENCY = 13
GL_PATCHES = 14
GL_POLYGON_STIPPLE_BIT = 16
GL_MAP_FLUSH_EXPLICIT_BIT = 16
GL_TESS_EVALUATION_SHADER_BIT = 16
GL_PIXEL_MODE_BIT = 32
GL_MAP_UNSYNCHRONIZED_BIT = 32
GL_SHADER_IMAGE_ACCESS_BARRIER_BIT = 32
GL_COMPUTE_SHADER_BIT = 32
GL_LIGHTING_BIT = 64
GL_COMMAND_BARRIER_BIT = 64
GL_MAP_PERSISTENT_BIT = 64
GL_MESH_SHADER_BIT_NV = 64
GL_FOG_BIT = 128
GL_PIXEL_BUFFER_BARRIER_BIT = 128
GL_MAP_COHERENT_BIT = 128
GL_TASK_SHADER_BIT_NV = 128
GL_DEPTH_BUFFER_BIT = 256
GL_ACCUM = 256
GL_TEXTURE_UPDATE_BARRIER_BIT = 256
GL_DYNAMIC_STORAGE_BIT = 256
GL_LOAD = 257
GL_RETURN = 258
GL_MULT = 259
GL_ADD = 260
GL_NEVER = 512
GL_ACCUM_BUFFER_BIT = 512
GL_BUFFER_UPDATE_BARRIER_BIT = 512
GL_CLIENT_STORAGE_BIT = 512
GL_LESS = 513
GL_EQUAL = 514
GL_LEQUAL = 515
GL_GREATER = 516
GL_NOTEQUAL = 517
GL_GEQUAL = 518
GL_ALWAYS = 519
GL_SRC_COLOR = 768
GL_ONE_MINUS_SRC_COLOR = 769
GL_SRC_ALPHA = 770
GL_ONE_MINUS_SRC_ALPHA = 771
GL_DST_ALPHA = 772
GL_ONE_MINUS_DST_ALPHA = 773
GL_DST_COLOR = 774
GL_ONE_MINUS_DST_COLOR = 775
GL_SRC_ALPHA_SATURATE = 776
GL_STENCIL_BUFFER_BIT = 1024
GL_FRONT_LEFT = 1024
GL_FRAMEBUFFER_BARRIER_BIT = 1024
GL_FRONT_RIGHT = 1025
GL_BACK_LEFT = 1026
GL_BACK_RIGHT = 1027
GL_FRONT = 1028
GL_BACK = 1029
GL_LEFT = 1030
GL_RIGHT = 1031
GL_FRONT_AND_BACK = 1032
GL_AUX0 = 1033
GL_AUX1 = 1034
GL_AUX2 = 1035
GL_AUX3 = 1036
GL_INVALID_ENUM = 1280
GL_INVALID_VALUE = 1281
GL_INVALID_OPERATION = 1282
GL_STACK_OVERFLOW = 1283
GL_STACK_UNDERFLOW = 1284
GL_OUT_OF_MEMORY = 1285
GL_INVALID_FRAMEBUFFER_OPERATION = 1286
GL_INVALID_FRAMEBUFFER_OPERATION_EXT = 1286
GL_CONTEXT_LOST = 1287
GL_2D = 1536
GL_3D = 1537
GL_3D_COLOR = 1538
GL_3D_COLOR_TEXTURE = 1539
GL_4D_COLOR_TEXTURE = 1540
GL_PASS_THROUGH_TOKEN = 1792
GL_POINT_TOKEN = 1793
GL_LINE_TOKEN = 1794
GL_POLYGON_TOKEN = 1795
GL_BITMAP_TOKEN = 1796
GL_DRAW_PIXEL_TOKEN = 1797
GL_COPY_PIXEL_TOKEN = 1798
GL_LINE_RESET_TOKEN = 1799
GL_VIEWPORT_BIT = 2048
GL_EXP = 2048
GL_TRANSFORM_FEEDBACK_BARRIER_BIT = 2048
GL_EXP2 = 2049
GL_CW = 2304
GL_CCW = 2305
GL_COEFF = 2560
GL_ORDER = 2561
GL_DOMAIN = 2562
GL_CURRENT_COLOR = 2816
GL_CURRENT_INDEX = 2817
GL_CURRENT_NORMAL = 2818
GL_CURRENT_TEXTURE_COORDS = 2819
GL_CURRENT_RASTER_COLOR = 2820
GL_CURRENT_RASTER_INDEX = 2821
GL_CURRENT_RASTER_TEXTURE_COORDS = 2822
GL_CURRENT_RASTER_POSITION = 2823
GL_CURRENT_RASTER_POSITION_VALID = 2824
GL_CURRENT_RASTER_DISTANCE = 2825
GL_POINT_SMOOTH = 2832
GL_POINT_SIZE = 2833
GL_POINT_SIZE_RANGE = 2834
GL_SMOOTH_POINT_SIZE_RANGE = 2834
GL_POINT_SIZE_GRANULARITY = 2835
GL_SMOOTH_POINT_SIZE_GRANULARITY = 2835
GL_LINE_SMOOTH = 2848
GL_LINE_WIDTH = 2849
GL_LINE_WIDTH_RANGE = 2850
GL_SMOOTH_LINE_WIDTH_RANGE = 2850
GL_LINE_WIDTH_GRANULARITY = 2851
GL_SMOOTH_LINE_WIDTH_GRANULARITY = 2851
GL_LINE_STIPPLE = 2852
GL_LINE_STIPPLE_PATTERN = 2853
GL_LINE_STIPPLE_REPEAT = 2854
GL_LIST_MODE = 2864
GL_MAX_LIST_NESTING = 2865
GL_LIST_BASE = 2866
GL_LIST_INDEX = 2867
GL_POLYGON_MODE = 2880
GL_POLYGON_SMOOTH = 2881
GL_POLYGON_STIPPLE = 2882
GL_EDGE_FLAG = 2883
GL_CULL_FACE = 2884
GL_CULL_FACE_MODE = 2885
GL_FRONT_FACE = 2886
GL_LIGHTING = 2896
GL_LIGHT_MODEL_LOCAL_VIEWER = 2897
GL_LIGHT_MODEL_TWO_SIDE = 2898
GL_LIGHT_MODEL_AMBIENT = 2899
GL_SHADE_MODEL = 2900
GL_COLOR_MATERIAL_FACE = 2901
GL_COLOR_MATERIAL_PARAMETER = 2902
GL_COLOR_MATERIAL = 2903
GL_FOG = 2912
GL_FOG_INDEX = 2913
GL_FOG_DENSITY = 2914
GL_FOG_START = 2915
GL_FOG_END = 2916
GL_FOG_MODE = 2917
GL_FOG_COLOR = 2918
GL_DEPTH_RANGE = 2928
GL_DEPTH_TEST = 2929
GL_DEPTH_WRITEMASK = 2930
GL_DEPTH_CLEAR_VALUE = 2931
GL_DEPTH_FUNC = 2932
GL_ACCUM_CLEAR_VALUE = 2944
GL_STENCIL_TEST = 2960
GL_STENCIL_CLEAR_VALUE = 2961
GL_STENCIL_FUNC = 2962
GL_STENCIL_VALUE_MASK = 2963
GL_STENCIL_FAIL = 2964
GL_STENCIL_PASS_DEPTH_FAIL = 2965
GL_STENCIL_PASS_DEPTH_PASS = 2966
GL_STENCIL_REF = 2967
GL_STENCIL_WRITEMASK = 2968
GL_MATRIX_MODE = 2976
GL_NORMALIZE = 2977
GL_VIEWPORT = 2978
GL_MODELVIEW_STACK_DEPTH = 2979
GL_PROJECTION_STACK_DEPTH = 2980
GL_TEXTURE_STACK_DEPTH = 2981
GL_MODELVIEW_MATRIX = 2982
GL_PROJECTION_MATRIX = 2983
GL_TEXTURE_MATRIX = 2984
GL_ATTRIB_STACK_DEPTH = 2992
GL_CLIENT_ATTRIB_STACK_DEPTH = 2993
GL_ALPHA_TEST = 3008
GL_ALPHA_TEST_FUNC = 3009
GL_ALPHA_TEST_REF = 3010
GL_DITHER = 3024
GL_BLEND_DST = 3040
GL_BLEND_SRC = 3041
GL_BLEND = 3042
GL_LOGIC_OP_MODE = 3056
GL_LOGIC_OP = 3057
GL_INDEX_LOGIC_OP = 3057
GL_COLOR_LOGIC_OP = 3058
GL_AUX_BUFFERS = 3072
GL_DRAW_BUFFER = 3073
GL_READ_BUFFER = 3074
GL_SCISSOR_BOX = 3088
GL_SCISSOR_TEST = 3089
GL_INDEX_CLEAR_VALUE = 3104
GL_INDEX_WRITEMASK = 3105
GL_COLOR_CLEAR_VALUE = 3106
GL_COLOR_WRITEMASK = 3107
GL_INDEX_MODE = 3120
GL_RGBA_MODE = 3121
GL_DOUBLEBUFFER = 3122
GL_STEREO = 3123
GL_RENDER_MODE = 3136
GL_PERSPECTIVE_CORRECTION_HINT = 3152
GL_POINT_SMOOTH_HINT = 3153
GL_LINE_SMOOTH_HINT = 3154
GL_POLYGON_SMOOTH_HINT = 3155
GL_FOG_HINT = 3156
GL_TEXTURE_GEN_S = 3168
GL_TEXTURE_GEN_T = 3169
GL_TEXTURE_GEN_R = 3170
GL_TEXTURE_GEN_Q = 3171
GL_PIXEL_MAP_I_TO_I = 3184
GL_PIXEL_MAP_S_TO_S = 3185
GL_PIXEL_MAP_I_TO_R = 3186
GL_PIXEL_MAP_I_TO_G = 3187
GL_PIXEL_MAP_I_TO_B = 3188
GL_PIXEL_MAP_I_TO_A = 3189
GL_PIXEL_MAP_R_TO_R = 3190
GL_PIXEL_MAP_G_TO_G = 3191
GL_PIXEL_MAP_B_TO_B = 3192
GL_PIXEL_MAP_A_TO_A = 3193
GL_PIXEL_MAP_I_TO_I_SIZE = 3248
GL_PIXEL_MAP_S_TO_S_SIZE = 3249
GL_PIXEL_MAP_I_TO_R_SIZE = 3250
GL_PIXEL_MAP_I_TO_G_SIZE = 3251
GL_PIXEL_MAP_I_TO_B_SIZE = 3252
GL_PIXEL_MAP_I_TO_A_SIZE = 3253
GL_PIXEL_MAP_R_TO_R_SIZE = 3254
GL_PIXEL_MAP_G_TO_G_SIZE = 3255
GL_PIXEL_MAP_B_TO_B_SIZE = 3256
GL_PIXEL_MAP_A_TO_A_SIZE = 3257
GL_UNPACK_SWAP_BYTES = 3312
GL_UNPACK_LSB_FIRST = 3313
GL_UNPACK_ROW_LENGTH = 3314
GL_UNPACK_SKIP_ROWS = 3315
GL_UNPACK_SKIP_PIXELS = 3316
GL_UNPACK_ALIGNMENT = 3317
GL_PACK_SWAP_BYTES = 3328
GL_PACK_LSB_FIRST = 3329
GL_PACK_ROW_LENGTH = 3330
GL_PACK_SKIP_ROWS = 3331
GL_PACK_SKIP_PIXELS = 3332
GL_PACK_ALIGNMENT = 3333
GL_MAP_COLOR = 3344
GL_MAP_STENCIL = 3345
GL_INDEX_SHIFT = 3346
GL_INDEX_OFFSET = 3347
GL_RED_SCALE = 3348
GL_RED_BIAS = 3349
GL_ZOOM_X = 3350
GL_ZOOM_Y = 3351
GL_GREEN_SCALE = 3352
GL_GREEN_BIAS = 3353
GL_BLUE_SCALE = 3354
GL_BLUE_BIAS = 3355
GL_ALPHA_SCALE = 3356
GL_ALPHA_BIAS = 3357
GL_DEPTH_SCALE = 3358
GL_DEPTH_BIAS = 3359
GL_MAX_EVAL_ORDER = 3376
GL_MAX_LIGHTS = 3377
GL_MAX_CLIP_PLANES = 3378
GL_MAX_CLIP_DISTANCES = 3378
GL_MAX_TEXTURE_SIZE = 3379
GL_MAX_PIXEL_MAP_TABLE = 3380
GL_MAX_ATTRIB_STACK_DEPTH = 3381
GL_MAX_MODELVIEW_STACK_DEPTH = 3382
GL_MAX_NAME_STACK_DEPTH = 3383
GL_MAX_PROJECTION_STACK_DEPTH = 3384
GL_MAX_TEXTURE_STACK_DEPTH = 3385
GL_MAX_VIEWPORT_DIMS = 3386
GL_MAX_CLIENT_ATTRIB_STACK_DEPTH = 3387
GL_SUBPIXEL_BITS = 3408
GL_INDEX_BITS = 3409
GL_RED_BITS = 3410
GL_GREEN_BITS = 3411
GL_BLUE_BITS = 3412
GL_ALPHA_BITS = 3413
GL_DEPTH_BITS = 3414
GL_STENCIL_BITS = 3415
GL_ACCUM_RED_BITS = 3416
GL_ACCUM_GREEN_BITS = 3417
GL_ACCUM_BLUE_BITS = 3418
GL_ACCUM_ALPHA_BITS = 3419
GL_NAME_STACK_DEPTH = 3440
GL_AUTO_NORMAL = 3456
GL_MAP1_COLOR_4 = 3472
GL_MAP1_INDEX = 3473
GL_MAP1_NORMAL = 3474
GL_MAP1_TEXTURE_COORD_1 = 3475
GL_MAP1_TEXTURE_COORD_2 = 3476
GL_MAP1_TEXTURE_COORD_3 = 3477
GL_MAP1_TEXTURE_COORD_4 = 3478
GL_MAP1_VERTEX_3 = 3479
GL_MAP1_VERTEX_4 = 3480
GL_MAP2_COLOR_4 = 3504
GL_MAP2_INDEX = 3505
GL_MAP2_NORMAL = 3506
GL_MAP2_TEXTURE_COORD_1 = 3507
GL_MAP2_TEXTURE_COORD_2 = 3508
GL_MAP2_TEXTURE_COORD_3 = 3509
GL_MAP2_TEXTURE_COORD_4 = 3510
GL_MAP2_VERTEX_3 = 3511
GL_MAP2_VERTEX_4 = 3512
GL_MAP1_GRID_DOMAIN = 3536
GL_MAP1_GRID_SEGMENTS = 3537
GL_MAP2_GRID_DOMAIN = 3538
GL_MAP2_GRID_SEGMENTS = 3539
GL_TEXTURE_1D = 3552
GL_TEXTURE_2D = 3553
GL_FEEDBACK_BUFFER_POINTER = 3568
GL_FEEDBACK_BUFFER_SIZE = 3569
GL_FEEDBACK_BUFFER_TYPE = 3570
GL_SELECTION_BUFFER_POINTER = 3571
GL_SELECTION_BUFFER_SIZE = 3572
GL_TEXTURE_WIDTH = 4096
GL_TRANSFORM_BIT = 4096
GL_ATOMIC_COUNTER_BARRIER_BIT = 4096
GL_TEXTURE_HEIGHT = 4097
GL_TEXTURE_COMPONENTS = 4099
GL_TEXTURE_INTERNAL_FORMAT = 4099
GL_TEXTURE_BORDER_COLOR = 4100
GL_TEXTURE_BORDER = 4101
GL_TEXTURE_TARGET = 4102
GL_DONT_CARE = 4352
GL_FASTEST = 4353
GL_NICEST = 4354
GL_AMBIENT = 4608
GL_DIFFUSE = 4609
GL_SPECULAR = 4610
GL_POSITION = 4611
GL_SPOT_DIRECTION = 4612
GL_SPOT_EXPONENT = 4613
GL_SPOT_CUTOFF = 4614
GL_CONSTANT_ATTENUATION = 4615
GL_LINEAR_ATTENUATION = 4616
GL_QUADRATIC_ATTENUATION = 4617
GL_COMPILE = 4864
GL_COMPILE_AND_EXECUTE = 4865
GL_BYTE = 5120
GL_UNSIGNED_BYTE = 5121
GL_SHORT = 5122
GL_UNSIGNED_SHORT = 5123
GL_INT = 5124
GL_UNSIGNED_INT = 5125
GL_FLOAT = 5126
GL_2_BYTES = 5127
GL_3_BYTES = 5128
GL_4_BYTES = 5129
GL_DOUBLE = 5130
GL_HALF_FLOAT = 5131
GL_FIXED = 5132
GL_INT64_ARB = 5134
GL_UNSIGNED_INT64_ARB = 5135
GL_CLEAR = 5376
GL_AND = 5377
GL_AND_REVERSE = 5378
GL_COPY = 5379
GL_AND_INVERTED = 5380
GL_NOOP = 5381
GL_XOR = 5382
GL_OR = 5383
GL_NOR = 5384
GL_EQUIV = 5385
GL_INVERT = 5386
GL_OR_REVERSE = 5387
GL_COPY_INVERTED = 5388
GL_OR_INVERTED = 5389
GL_NAND = 5390
GL_SET = 5391
GL_EMISSION = 5632
GL_SHININESS = 5633
GL_AMBIENT_AND_DIFFUSE = 5634
GL_COLOR_INDEXES = 5635
GL_MODELVIEW = 5888
GL_PROJECTION = 5889
GL_TEXTURE = 5890
GL_COLOR = 6144
GL_DEPTH = 6145
GL_STENCIL = 6146
GL_COLOR_INDEX = 6400
GL_STENCIL_INDEX = 6401
GL_DEPTH_COMPONENT = 6402
GL_RED = 6403
GL_GREEN = 6404
GL_BLUE = 6405
GL_ALPHA = 6406
GL_RGB = 6407
GL_RGBA = 6408
GL_LUMINANCE = 6409
GL_LUMINANCE_ALPHA = 6410
GL_BITMAP = 6656
GL_POINT = 6912
GL_LINE = 6913
GL_FILL = 6914
GL_RENDER = 7168
GL_FEEDBACK = 7169
GL_SELECT = 7170
GL_FLAT = 7424
GL_SMOOTH = 7425
GL_KEEP = 7680
GL_REPLACE = 7681
GL_INCR = 7682
GL_DECR = 7683
GL_VENDOR = 7936
GL_RENDERER = 7937
GL_VERSION = 7938
GL_EXTENSIONS = 7939
GL_ENABLE_BIT = 8192
GL_S = 8192
GL_SHADER_STORAGE_BARRIER_BIT = 8192
GL_T = 8193
GL_R = 8194
GL_Q = 8195
GL_MODULATE = 8448
GL_DECAL = 8449
GL_TEXTURE_ENV_MODE = 8704
GL_TEXTURE_ENV_COLOR = 8705
GL_TEXTURE_ENV = 8960
GL_EYE_LINEAR = 9216
GL_OBJECT_LINEAR = 9217
GL_SPHERE_MAP = 9218
GL_TEXTURE_GEN_MODE = 9472
GL_OBJECT_PLANE = 9473
GL_EYE_PLANE = 9474
GL_NEAREST = 9728
GL_LINEAR = 9729
GL_NEAREST_MIPMAP_NEAREST = 9984
GL_LINEAR_MIPMAP_NEAREST = 9985
GL_NEAREST_MIPMAP_LINEAR = 9986
GL_LINEAR_MIPMAP_LINEAR = 9987
GL_TEXTURE_MAG_FILTER = 10240
GL_TEXTURE_MIN_FILTER = 10241
GL_TEXTURE_WRAP_S = 10242
GL_TEXTURE_WRAP_T = 10243
GL_CLAMP = 10496
GL_REPEAT = 10497
GL_POLYGON_OFFSET_UNITS = 10752
GL_POLYGON_OFFSET_POINT = 10753
GL_POLYGON_OFFSET_LINE = 10754
GL_R3_G3_B2 = 10768
GL_V2F = 10784
GL_V3F = 10785
GL_C4UB_V2F = 10786
GL_C4UB_V3F = 10787
GL_C3F_V3F = 10788
GL_N3F_V3F = 10789
GL_C4F_N3F_V3F = 10790
GL_T2F_V3F = 10791
GL_T4F_V4F = 10792
GL_T2F_C4UB_V3F = 10793
GL_T2F_C3F_V3F = 10794
GL_T2F_N3F_V3F = 10795
GL_T2F_C4F_N3F_V3F = 10796
GL_T4F_C4F_N3F_V4F = 10797
GL_CLIP_PLANE0 = 12288
GL_CLIP_DISTANCE0 = 12288
GL_CLIP_PLANE1 = 12289
GL_CLIP_DISTANCE1 = 12289
GL_CLIP_PLANE2 = 12290
GL_CLIP_DISTANCE2 = 12290
GL_CLIP_PLANE3 = 12291
GL_CLIP_DISTANCE3 = 12291
GL_CLIP_PLANE4 = 12292
GL_CLIP_DISTANCE4 = 12292
GL_CLIP_PLANE5 = 12293
GL_CLIP_DISTANCE5 = 12293
GL_CLIP_DISTANCE6 = 12294
GL_CLIP_DISTANCE7 = 12295
GL_COLOR_BUFFER_BIT = 16384
GL_LIGHT0 = 16384
GL_CLIENT_MAPPED_BUFFER_BARRIER_BIT = 16384
GL_LIGHT1 = 16385
GL_LIGHT2 = 16386
GL_LIGHT3 = 16387
GL_LIGHT4 = 16388
GL_LIGHT5 = 16389
GL_LIGHT6 = 16390
GL_LIGHT7 = 16391
GL_HINT_BIT = 32768
GL_QUERY_BUFFER_BARRIER_BIT = 32768
GL_CONSTANT_COLOR = 32769
GL_ONE_MINUS_CONSTANT_COLOR = 32770
GL_CONSTANT_ALPHA = 32771
GL_ONE_MINUS_CONSTANT_ALPHA = 32772
GL_BLEND_COLOR = 32773
GL_FUNC_ADD = 32774
GL_MIN = 32775
GL_MAX = 32776
GL_BLEND_EQUATION = 32777
GL_BLEND_EQUATION_RGB = 32777
GL_FUNC_SUBTRACT = 32778
GL_FUNC_REVERSE_SUBTRACT = 32779
GL_CONVOLUTION_1D = 32784
GL_CONVOLUTION_2D = 32785
GL_SEPARABLE_2D = 32786
GL_HISTOGRAM = 32804
GL_PROXY_HISTOGRAM = 32805
GL_MINMAX = 32814
GL_UNSIGNED_BYTE_3_3_2 = 32818
GL_UNSIGNED_SHORT_4_4_4_4 = 32819
GL_UNSIGNED_SHORT_5_5_5_1 = 32820
GL_UNSIGNED_INT_8_8_8_8 = 32821
GL_UNSIGNED_INT_10_10_10_2 = 32822
GL_POLYGON_OFFSET_FILL = 32823
GL_POLYGON_OFFSET_FACTOR = 32824
GL_RESCALE_NORMAL = 32826
GL_ALPHA4 = 32827
GL_ALPHA8 = 32828
GL_ALPHA12 = 32829
GL_ALPHA16 = 32830
GL_LUMINANCE4 = 32831
GL_LUMINANCE8 = 32832
GL_LUMINANCE12 = 32833
GL_LUMINANCE16 = 32834
GL_LUMINANCE4_ALPHA4 = 32835
GL_LUMINANCE6_ALPHA2 = 32836
GL_LUMINANCE8_ALPHA8 = 32837
GL_LUMINANCE12_ALPHA4 = 32838
GL_LUMINANCE12_ALPHA12 = 32839
GL_LUMINANCE16_ALPHA16 = 32840
GL_INTENSITY = 32841
GL_INTENSITY4 = 32842
GL_INTENSITY8 = 32843
GL_INTENSITY12 = 32844
GL_INTENSITY16 = 32845
GL_RGB4 = 32847
GL_RGB5 = 32848
GL_RGB8 = 32849
GL_RGB10 = 32850
GL_RGB12 = 32851
GL_RGB16 = 32852
GL_RGBA2 = 32853
GL_RGBA4 = 32854
GL_RGB5_A1 = 32855
GL_RGBA8 = 32856
GL_RGB10_A2 = 32857
GL_RGBA12 = 32858
GL_RGBA16 = 32859
GL_TEXTURE_RED_SIZE = 32860
GL_TEXTURE_GREEN_SIZE = 32861
GL_TEXTURE_BLUE_SIZE = 32862
GL_TEXTURE_ALPHA_SIZE = 32863
GL_TEXTURE_LUMINANCE_SIZE = 32864
GL_TEXTURE_INTENSITY_SIZE = 32865
GL_PROXY_TEXTURE_1D = 32867
GL_PROXY_TEXTURE_2D = 32868
GL_TEXTURE_PRIORITY = 32870
GL_TEXTURE_RESIDENT = 32871
GL_TEXTURE_BINDING_1D = 32872
GL_TEXTURE_BINDING_2D = 32873
GL_TEXTURE_BINDING_3D = 32874
GL_PACK_SKIP_IMAGES = 32875
GL_PACK_IMAGE_HEIGHT = 32876
GL_UNPACK_SKIP_IMAGES = 32877
GL_UNPACK_IMAGE_HEIGHT = 32878
GL_TEXTURE_3D = 32879
GL_PROXY_TEXTURE_3D = 32880
GL_TEXTURE_DEPTH = 32881
GL_TEXTURE_WRAP_R = 32882
GL_MAX_3D_TEXTURE_SIZE = 32883
GL_VERTEX_ARRAY = 32884
GL_NORMAL_ARRAY = 32885
GL_COLOR_ARRAY = 32886
GL_INDEX_ARRAY = 32887
GL_TEXTURE_COORD_ARRAY = 32888
GL_EDGE_FLAG_ARRAY = 32889
GL_VERTEX_ARRAY_SIZE = 32890
GL_VERTEX_ARRAY_TYPE = 32891
GL_VERTEX_ARRAY_STRIDE = 32892
GL_NORMAL_ARRAY_TYPE = 32894
GL_NORMAL_ARRAY_STRIDE = 32895
GL_COLOR_ARRAY_SIZE = 32897
GL_COLOR_ARRAY_TYPE = 32898
GL_COLOR_ARRAY_STRIDE = 32899
GL_INDEX_ARRAY_TYPE = 32901
GL_INDEX_ARRAY_STRIDE = 32902
GL_TEXTURE_COORD_ARRAY_SIZE = 32904
GL_TEXTURE_COORD_ARRAY_TYPE = 32905
GL_TEXTURE_COORD_ARRAY_STRIDE = 32906
GL_EDGE_FLAG_ARRAY_STRIDE = 32908
GL_VERTEX_ARRAY_POINTER = 32910
GL_NORMAL_ARRAY_POINTER = 32911
GL_COLOR_ARRAY_POINTER = 32912
GL_INDEX_ARRAY_POINTER = 32913
GL_TEXTURE_COORD_ARRAY_POINTER = 32914
GL_EDGE_FLAG_ARRAY_POINTER = 32915
GL_MULTISAMPLE = 32925
GL_MULTISAMPLE_ARB = 32925
GL_SAMPLE_ALPHA_TO_COVERAGE = 32926
GL_SAMPLE_ALPHA_TO_COVERAGE_ARB = 32926
GL_SAMPLE_ALPHA_TO_ONE = 32927
GL_SAMPLE_ALPHA_TO_ONE_ARB = 32927
GL_SAMPLE_COVERAGE = 32928
GL_SAMPLE_COVERAGE_ARB = 32928
GL_SAMPLE_BUFFERS = 32936
GL_SAMPLE_BUFFERS_ARB = 32936
GL_SAMPLES = 32937
GL_SAMPLES_ARB = 32937
GL_SAMPLE_COVERAGE_VALUE = 32938
GL_SAMPLE_COVERAGE_VALUE_ARB = 32938
GL_SAMPLE_COVERAGE_INVERT = 32939
GL_SAMPLE_COVERAGE_INVERT_ARB = 32939
GL_BLEND_DST_RGB = 32968
GL_BLEND_SRC_RGB = 32969
GL_BLEND_DST_ALPHA = 32970
GL_BLEND_SRC_ALPHA = 32971
GL_COLOR_TABLE = 32976
GL_POST_CONVOLUTION_COLOR_TABLE = 32977
GL_POST_COLOR_MATRIX_COLOR_TABLE = 32978
GL_PROXY_COLOR_TABLE = 32979
GL_PROXY_POST_CONVOLUTION_COLOR_TABLE = 32980
GL_PROXY_POST_COLOR_MATRIX_COLOR_TABLE = 32981
GL_BGR = 32992
GL_BGRA = 32993
GL_MAX_ELEMENTS_VERTICES = 33000
GL_MAX_ELEMENTS_INDICES = 33001
GL_PARAMETER_BUFFER = 33006
GL_PARAMETER_BUFFER_BINDING = 33007
GL_POINT_SIZE_MIN = 33062
GL_POINT_SIZE_MAX = 33063
GL_POINT_FADE_THRESHOLD_SIZE = 33064
GL_POINT_DISTANCE_ATTENUATION = 33065
GL_CLAMP_TO_BORDER = 33069
GL_CLAMP_TO_EDGE = 33071
GL_TEXTURE_MIN_LOD = 33082
GL_TEXTURE_MAX_LOD = 33083
GL_TEXTURE_BASE_LEVEL = 33084
GL_TEXTURE_MAX_LEVEL = 33085
GL_GENERATE_MIPMAP = 33169
GL_GENERATE_MIPMAP_HINT = 33170
GL_DEPTH_COMPONENT16 = 33189
GL_DEPTH_COMPONENT24 = 33190
GL_DEPTH_COMPONENT32 = 33191
GL_LIGHT_MODEL_COLOR_CONTROL = 33272
GL_SINGLE_COLOR = 33273
GL_SEPARATE_SPECULAR_COLOR = 33274
GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING = 33296
GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE = 33297
GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE = 33298
GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE = 33299
GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE = 33300
GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE = 33301
GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE = 33302
GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE = 33303
GL_FRAMEBUFFER_DEFAULT = 33304
GL_FRAMEBUFFER_UNDEFINED = 33305
GL_DEPTH_STENCIL_ATTACHMENT = 33306
GL_MAJOR_VERSION = 33307
GL_MINOR_VERSION = 33308
GL_NUM_EXTENSIONS = 33309
GL_CONTEXT_FLAGS = 33310
GL_BUFFER_IMMUTABLE_STORAGE = 33311
GL_BUFFER_STORAGE_FLAGS = 33312
GL_PRIMITIVE_RESTART_FOR_PATCHES_SUPPORTED = 33313
GL_INDEX = 33314
GL_COMPRESSED_RED = 33317
GL_COMPRESSED_RG = 33318
GL_RG = 33319
GL_RG_INTEGER = 33320
GL_R8 = 33321
GL_R16 = 33322
GL_RG8 = 33323
GL_RG16 = 33324
GL_R16F = 33325
GL_R32F = 33326
GL_RG16F = 33327
GL_RG32F = 33328
GL_R8I = 33329
GL_R8UI = 33330
GL_R16I = 33331
GL_R16UI = 33332
GL_R32I = 33333
GL_R32UI = 33334
GL_RG8I = 33335
GL_RG8UI = 33336
GL_RG16I = 33337
GL_RG16UI = 33338
GL_RG32I = 33339
GL_RG32UI = 33340
GL_DEBUG_OUTPUT_SYNCHRONOUS = 33346
GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH = 33347
GL_DEBUG_CALLBACK_FUNCTION = 33348
GL_DEBUG_CALLBACK_USER_PARAM = 33349
GL_DEBUG_SOURCE_API = 33350
GL_DEBUG_SOURCE_WINDOW_SYSTEM = 33351
GL_DEBUG_SOURCE_SHADER_COMPILER = 33352
GL_DEBUG_SOURCE_THIRD_PARTY = 33353
GL_DEBUG_SOURCE_APPLICATION = 33354
GL_DEBUG_SOURCE_OTHER = 33355
GL_DEBUG_TYPE_ERROR = 33356
GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR = 33357
GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR = 33358
GL_DEBUG_TYPE_PORTABILITY = 33359
GL_DEBUG_TYPE_PERFORMANCE = 33360
GL_DEBUG_TYPE_OTHER = 33361
GL_LOSE_CONTEXT_ON_RESET = 33362
GL_GUILTY_CONTEXT_RESET = 33363
GL_INNOCENT_CONTEXT_RESET = 33364
GL_UNKNOWN_CONTEXT_RESET = 33365
GL_RESET_NOTIFICATION_STRATEGY = 33366
GL_PROGRAM_BINARY_RETRIEVABLE_HINT = 33367
GL_PROGRAM_SEPARABLE = 33368
GL_ACTIVE_PROGRAM = 33369
GL_PROGRAM_PIPELINE_BINDING = 33370
GL_MAX_VIEWPORTS = 33371
GL_VIEWPORT_SUBPIXEL_BITS = 33372
GL_VIEWPORT_BOUNDS_RANGE = 33373
GL_LAYER_PROVOKING_VERTEX = 33374
GL_VIEWPORT_INDEX_PROVOKING_VERTEX = 33375
GL_UNDEFINED_VERTEX = 33376
GL_NO_RESET_NOTIFICATION = 33377
GL_MAX_COMPUTE_SHARED_MEMORY_SIZE = 33378
GL_MAX_COMPUTE_UNIFORM_COMPONENTS = 33379
GL_MAX_COMPUTE_ATOMIC_COUNTER_BUFFERS = 33380
GL_MAX_COMPUTE_ATOMIC_COUNTERS = 33381
GL_MAX_COMBINED_COMPUTE_UNIFORM_COMPONENTS = 33382
GL_COMPUTE_WORK_GROUP_SIZE = 33383
GL_DEBUG_TYPE_MARKER = 33384
GL_DEBUG_TYPE_PUSH_GROUP = 33385
GL_DEBUG_TYPE_POP_GROUP = 33386
GL_DEBUG_SEVERITY_NOTIFICATION = 33387
GL_MAX_DEBUG_GROUP_STACK_DEPTH = 33388
GL_DEBUG_GROUP_STACK_DEPTH = 33389
GL_MAX_UNIFORM_LOCATIONS = 33390
GL_INTERNALFORMAT_SUPPORTED = 33391
GL_INTERNALFORMAT_PREFERRED = 33392
GL_INTERNALFORMAT_RED_SIZE = 33393
GL_INTERNALFORMAT_GREEN_SIZE = 33394
GL_INTERNALFORMAT_BLUE_SIZE = 33395
GL_INTERNALFORMAT_ALPHA_SIZE = 33396
GL_INTERNALFORMAT_DEPTH_SIZE = 33397
GL_INTERNALFORMAT_STENCIL_SIZE = 33398
GL_INTERNALFORMAT_SHARED_SIZE = 33399
GL_INTERNALFORMAT_RED_TYPE = 33400
GL_INTERNALFORMAT_GREEN_TYPE = 33401
GL_INTERNALFORMAT_BLUE_TYPE = 33402
GL_INTERNALFORMAT_ALPHA_TYPE = 33403
GL_INTERNALFORMAT_DEPTH_TYPE = 33404
GL_INTERNALFORMAT_STENCIL_TYPE = 33405
GL_MAX_WIDTH = 33406
GL_MAX_HEIGHT = 33407
GL_MAX_DEPTH = 33408
GL_MAX_LAYERS = 33409
GL_MAX_COMBINED_DIMENSIONS = 33410
GL_COLOR_COMPONENTS = 33411
GL_DEPTH_COMPONENTS = 33412
GL_STENCIL_COMPONENTS = 33413
GL_COLOR_RENDERABLE = 33414
GL_DEPTH_RENDERABLE = 33415
GL_STENCIL_RENDERABLE = 33416
GL_FRAMEBUFFER_RENDERABLE = 33417
GL_FRAMEBUFFER_RENDERABLE_LAYERED = 33418
GL_FRAMEBUFFER_BLEND = 33419
GL_READ_PIXELS = 33420
GL_READ_PIXELS_FORMAT = 33421
GL_READ_PIXELS_TYPE = 33422
GL_TEXTURE_IMAGE_FORMAT = 33423
GL_TEXTURE_IMAGE_TYPE = 33424
GL_GET_TEXTURE_IMAGE_FORMAT = 33425
GL_GET_TEXTURE_IMAGE_TYPE = 33426
GL_MIPMAP = 33427
GL_MANUAL_GENERATE_MIPMAP = 33428
GL_AUTO_GENERATE_MIPMAP = 33429
GL_COLOR_ENCODING = 33430
GL_SRGB_READ = 33431
GL_SRGB_WRITE = 33432
GL_FILTER = 33434
GL_VERTEX_TEXTURE = 33435
GL_TESS_CONTROL_TEXTURE = 33436
GL_TESS_EVALUATION_TEXTURE = 33437
GL_GEOMETRY_TEXTURE = 33438
GL_FRAGMENT_TEXTURE = 33439
GL_COMPUTE_TEXTURE = 33440
GL_TEXTURE_SHADOW = 33441
GL_TEXTURE_GATHER = 33442
GL_TEXTURE_GATHER_SHADOW = 33443
GL_SHADER_IMAGE_LOAD = 33444
GL_SHADER_IMAGE_STORE = 33445
GL_SHADER_IMAGE_ATOMIC = 33446
GL_IMAGE_TEXEL_SIZE = 33447
GL_IMAGE_COMPATIBILITY_CLASS = 33448
GL_IMAGE_PIXEL_FORMAT = 33449
GL_IMAGE_PIXEL_TYPE = 33450
GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_TEST = 33452
GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_TEST = 33453
GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_WRITE = 33454
GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_WRITE = 33455
GL_TEXTURE_COMPRESSED_BLOCK_WIDTH = 33457
GL_TEXTURE_COMPRESSED_BLOCK_HEIGHT = 33458
GL_TEXTURE_COMPRESSED_BLOCK_SIZE = 33459
GL_CLEAR_BUFFER = 33460
GL_TEXTURE_VIEW = 33461
GL_VIEW_COMPATIBILITY_CLASS = 33462
GL_FULL_SUPPORT = 33463
GL_CAVEAT_SUPPORT = 33464
GL_IMAGE_CLASS_4_X_32 = 33465
GL_IMAGE_CLASS_2_X_32 = 33466
GL_IMAGE_CLASS_1_X_32 = 33467
GL_IMAGE_CLASS_4_X_16 = 33468
GL_IMAGE_CLASS_2_X_16 = 33469
GL_IMAGE_CLASS_1_X_16 = 33470
GL_IMAGE_CLASS_4_X_8 = 33471
GL_IMAGE_CLASS_2_X_8 = 33472
GL_IMAGE_CLASS_1_X_8 = 33473
GL_IMAGE_CLASS_11_11_10 = 33474
GL_IMAGE_CLASS_10_10_10_2 = 33475
GL_VIEW_CLASS_128_BITS = 33476
GL_VIEW_CLASS_96_BITS = 33477
GL_VIEW_CLASS_64_BITS = 33478
GL_VIEW_CLASS_48_BITS = 33479
GL_VIEW_CLASS_32_BITS = 33480
GL_VIEW_CLASS_24_BITS = 33481
GL_VIEW_CLASS_16_BITS = 33482
GL_VIEW_CLASS_8_BITS = 33483
GL_VIEW_CLASS_S3TC_DXT1_RGB = 33484
GL_VIEW_CLASS_S3TC_DXT1_RGBA = 33485
GL_VIEW_CLASS_S3TC_DXT3_RGBA = 33486
GL_VIEW_CLASS_S3TC_DXT5_RGBA = 33487
GL_VIEW_CLASS_RGTC1_RED = 33488
GL_VIEW_CLASS_RGTC2_RG = 33489
GL_VIEW_CLASS_BPTC_UNORM = 33490
GL_VIEW_CLASS_BPTC_FLOAT = 33491
GL_VERTEX_ATTRIB_BINDING = 33492
GL_VERTEX_ATTRIB_RELATIVE_OFFSET = 33493
GL_VERTEX_BINDING_DIVISOR = 33494
GL_VERTEX_BINDING_OFFSET = 33495
GL_VERTEX_BINDING_STRIDE = 33496
GL_MAX_VERTEX_ATTRIB_RELATIVE_OFFSET = 33497
GL_MAX_VERTEX_ATTRIB_BINDINGS = 33498
GL_TEXTURE_VIEW_MIN_LEVEL = 33499
GL_TEXTURE_VIEW_NUM_LEVELS = 33500
GL_TEXTURE_VIEW_MIN_LAYER = 33501
GL_TEXTURE_VIEW_NUM_LAYERS = 33502
GL_TEXTURE_IMMUTABLE_LEVELS = 33503
GL_BUFFER = 33504
GL_SHADER = 33505
GL_PROGRAM = 33506
GL_QUERY = 33507
GL_PROGRAM_PIPELINE = 33508
GL_MAX_VERTEX_ATTRIB_STRIDE = 33509
GL_SAMPLER = 33510
GL_DISPLAY_LIST = 33511
GL_MAX_LABEL_LENGTH = 33512
GL_NUM_SHADING_LANGUAGE_VERSIONS = 33513
GL_QUERY_TARGET = 33514
GL_TRANSFORM_FEEDBACK_OVERFLOW = 33516
GL_TRANSFORM_FEEDBACK_STREAM_OVERFLOW = 33517
GL_VERTICES_SUBMITTED = 33518
GL_PRIMITIVES_SUBMITTED = 33519
GL_VERTEX_SHADER_INVOCATIONS = 33520
GL_TESS_CONTROL_SHADER_PATCHES = 33521
GL_TESS_EVALUATION_SHADER_INVOCATIONS = 33522
GL_GEOMETRY_SHADER_PRIMITIVES_EMITTED = 33523
GL_FRAGMENT_SHADER_INVOCATIONS = 33524
GL_COMPUTE_SHADER_INVOCATIONS = 33525
GL_CLIPPING_INPUT_PRIMITIVES = 33526
GL_CLIPPING_OUTPUT_PRIMITIVES = 33527
GL_MAX_CULL_DISTANCES = 33529
GL_MAX_COMBINED_CLIP_AND_CULL_DISTANCES = 33530
GL_CONTEXT_RELEASE_BEHAVIOR = 33531
GL_CONTEXT_RELEASE_BEHAVIOR_FLUSH = 33532
GL_UNSIGNED_BYTE_2_3_3_REV = 33634
GL_UNSIGNED_SHORT_5_6_5 = 33635
GL_UNSIGNED_SHORT_5_6_5_REV = 33636
GL_UNSIGNED_SHORT_4_4_4_4_REV = 33637
GL_UNSIGNED_SHORT_1_5_5_5_REV = 33638
GL_UNSIGNED_INT_8_8_8_8_REV = 33639
GL_UNSIGNED_INT_2_10_10_10_REV = 33640
GL_MIRRORED_REPEAT = 33648
GL_COMPRESSED_RGB_S3TC_DXT1_EXT = 33776
GL_COMPRESSED_RGBA_S3TC_DXT1_EXT = 33777
GL_COMPRESSED_RGBA_S3TC_DXT3_EXT = 33778
GL_COMPRESSED_RGBA_S3TC_DXT5_EXT = 33779
GL_FOG_COORDINATE_SOURCE = 33872
GL_FOG_COORD_SRC = 33872
GL_FOG_COORDINATE = 33873
GL_FOG_COORD = 33873
GL_FRAGMENT_DEPTH = 33874
GL_CURRENT_FOG_COORDINATE = 33875
GL_CURRENT_FOG_COORD = 33875
GL_FOG_COORDINATE_ARRAY_TYPE = 33876
GL_FOG_COORD_ARRAY_TYPE = 33876
GL_FOG_COORDINATE_ARRAY_STRIDE = 33877
GL_FOG_COORD_ARRAY_STRIDE = 33877
GL_FOG_COORDINATE_ARRAY_POINTER = 33878
GL_FOG_COORD_ARRAY_POINTER = 33878
GL_FOG_COORDINATE_ARRAY = 33879
GL_FOG_COORD_ARRAY = 33879
GL_COLOR_SUM = 33880
GL_CURRENT_SECONDARY_COLOR = 33881
GL_SECONDARY_COLOR_ARRAY_SIZE = 33882
GL_SECONDARY_COLOR_ARRAY_TYPE = 33883
GL_SECONDARY_COLOR_ARRAY_STRIDE = 33884
GL_SECONDARY_COLOR_ARRAY_POINTER = 33885
GL_SECONDARY_COLOR_ARRAY = 33886
GL_CURRENT_RASTER_SECONDARY_COLOR = 33887
GL_ALIASED_POINT_SIZE_RANGE = 33901
GL_ALIASED_LINE_WIDTH_RANGE = 33902
GL_TEXTURE0 = 33984
GL_TEXTURE1 = 33985
GL_TEXTURE2 = 33986
GL_TEXTURE3 = 33987
GL_TEXTURE4 = 33988
GL_TEXTURE5 = 33989
GL_TEXTURE6 = 33990
GL_TEXTURE7 = 33991
GL_TEXTURE8 = 33992
GL_TEXTURE9 = 33993
GL_TEXTURE10 = 33994
GL_TEXTURE11 = 33995
GL_TEXTURE12 = 33996
GL_TEXTURE13 = 33997
GL_TEXTURE14 = 33998
GL_TEXTURE15 = 33999
GL_TEXTURE16 = 34000
GL_TEXTURE17 = 34001
GL_TEXTURE18 = 34002
GL_TEXTURE19 = 34003
GL_TEXTURE20 = 34004
GL_TEXTURE21 = 34005
GL_TEXTURE22 = 34006
GL_TEXTURE23 = 34007
GL_TEXTURE24 = 34008
GL_TEXTURE25 = 34009
GL_TEXTURE26 = 34010
GL_TEXTURE27 = 34011
GL_TEXTURE28 = 34012
GL_TEXTURE29 = 34013
GL_TEXTURE30 = 34014
GL_TEXTURE31 = 34015
GL_ACTIVE_TEXTURE = 34016
GL_CLIENT_ACTIVE_TEXTURE = 34017
GL_MAX_TEXTURE_UNITS = 34018
GL_TRANSPOSE_MODELVIEW_MATRIX = 34019
GL_TRANSPOSE_PROJECTION_MATRIX = 34020
GL_TRANSPOSE_TEXTURE_MATRIX = 34021
GL_TRANSPOSE_COLOR_MATRIX = 34022
GL_SUBTRACT = 34023
GL_MAX_RENDERBUFFER_SIZE = 34024
GL_MAX_RENDERBUFFER_SIZE_EXT = 34024
GL_COMPRESSED_ALPHA = 34025
GL_COMPRESSED_LUMINANCE = 34026
GL_COMPRESSED_LUMINANCE_ALPHA = 34027
GL_COMPRESSED_INTENSITY = 34028
GL_COMPRESSED_RGB = 34029
GL_COMPRESSED_RGBA = 34030
GL_TEXTURE_COMPRESSION_HINT = 34031
GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_CONTROL_SHADER = 34032
GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_EVALUATION_SHADER = 34033
GL_TEXTURE_RECTANGLE = 34037
GL_TEXTURE_BINDING_RECTANGLE = 34038
GL_PROXY_TEXTURE_RECTANGLE = 34039
GL_MAX_RECTANGLE_TEXTURE_SIZE = 34040
GL_DEPTH_STENCIL = 34041
GL_UNSIGNED_INT_24_8 = 34042
GL_MAX_TEXTURE_LOD_BIAS = 34045
GL_TEXTURE_MAX_ANISOTROPY = 34046
GL_MAX_TEXTURE_MAX_ANISOTROPY = 34047
GL_TEXTURE_FILTER_CONTROL = 34048
GL_TEXTURE_LOD_BIAS = 34049
GL_INCR_WRAP = 34055
GL_DECR_WRAP = 34056
GL_NORMAL_MAP = 34065
GL_REFLECTION_MAP = 34066
GL_TEXTURE_CUBE_MAP = 34067
GL_TEXTURE_BINDING_CUBE_MAP = 34068
GL_TEXTURE_CUBE_MAP_POSITIVE_X = 34069
GL_TEXTURE_CUBE_MAP_NEGATIVE_X = 34070
GL_TEXTURE_CUBE_MAP_POSITIVE_Y = 34071
GL_TEXTURE_CUBE_MAP_NEGATIVE_Y = 34072
GL_TEXTURE_CUBE_MAP_POSITIVE_Z = 34073
GL_TEXTURE_CUBE_MAP_NEGATIVE_Z = 34074
GL_PROXY_TEXTURE_CUBE_MAP = 34075
GL_MAX_CUBE_MAP_TEXTURE_SIZE = 34076
GL_COMBINE = 34160
GL_COMBINE_RGB = 34161
GL_COMBINE_ALPHA = 34162
GL_RGB_SCALE = 34163
GL_ADD_SIGNED = 34164
GL_INTERPOLATE = 34165
GL_CONSTANT = 34166
GL_PRIMARY_COLOR = 34167
GL_PREVIOUS = 34168
GL_SOURCE0_RGB = 34176
GL_SRC0_RGB = 34176
GL_SOURCE1_RGB = 34177
GL_SRC1_RGB = 34177
GL_SOURCE2_RGB = 34178
GL_SRC2_RGB = 34178
GL_SOURCE0_ALPHA = 34184
GL_SRC0_ALPHA = 34184
GL_SOURCE1_ALPHA = 34185
GL_SRC1_ALPHA = 34185
GL_SOURCE2_ALPHA = 34186
GL_SRC2_ALPHA = 34186
GL_OPERAND0_RGB = 34192
GL_OPERAND1_RGB = 34193
GL_OPERAND2_RGB = 34194
GL_OPERAND0_ALPHA = 34200
GL_OPERAND1_ALPHA = 34201
GL_OPERAND2_ALPHA = 34202
GL_VERTEX_ARRAY_BINDING = 34229
GL_VERTEX_ATTRIB_ARRAY_ENABLED = 34338
GL_VERTEX_ATTRIB_ARRAY_SIZE = 34339
GL_VERTEX_ATTRIB_ARRAY_STRIDE = 34340
GL_VERTEX_ATTRIB_ARRAY_TYPE = 34341
GL_CURRENT_VERTEX_ATTRIB = 34342
GL_VERTEX_PROGRAM_POINT_SIZE = 34370
GL_PROGRAM_POINT_SIZE = 34370
GL_VERTEX_PROGRAM_TWO_SIDE = 34371
GL_VERTEX_ATTRIB_ARRAY_POINTER = 34373
GL_DEPTH_CLAMP = 34383
GL_TEXTURE_COMPRESSED_IMAGE_SIZE = 34464
GL_TEXTURE_COMPRESSED = 34465
GL_NUM_COMPRESSED_TEXTURE_FORMATS = 34466
GL_COMPRESSED_TEXTURE_FORMATS = 34467
GL_DOT3_RGB = 34478
GL_DOT3_RGBA = 34479
GL_PROGRAM_BINARY_LENGTH = 34625
GL_MIRROR_CLAMP_TO_EDGE = 34627
GL_VERTEX_ATTRIB_ARRAY_LONG = 34638
GL_BUFFER_SIZE = 34660
GL_BUFFER_USAGE = 34661
GL_NUM_PROGRAM_BINARY_FORMATS = 34814
GL_PROGRAM_BINARY_FORMATS = 34815
GL_STENCIL_BACK_FUNC = 34816
GL_STENCIL_BACK_FAIL = 34817
GL_STENCIL_BACK_PASS_DEPTH_FAIL = 34818
GL_STENCIL_BACK_PASS_DEPTH_PASS = 34819
GL_RGBA32F = 34836
GL_RGB32F = 34837
GL_RGBA16F = 34842
GL_RGB16F = 34843
GL_MAX_DRAW_BUFFERS = 34852
GL_DRAW_BUFFER0 = 34853
GL_DRAW_BUFFER1 = 34854
GL_DRAW_BUFFER2 = 34855
GL_DRAW_BUFFER3 = 34856
GL_DRAW_BUFFER4 = 34857
GL_DRAW_BUFFER5 = 34858
GL_DRAW_BUFFER6 = 34859
GL_DRAW_BUFFER7 = 34860
GL_DRAW_BUFFER8 = 34861
GL_DRAW_BUFFER9 = 34862
GL_DRAW_BUFFER10 = 34863
GL_DRAW_BUFFER11 = 34864
GL_DRAW_BUFFER12 = 34865
GL_DRAW_BUFFER13 = 34866
GL_DRAW_BUFFER14 = 34867
GL_DRAW_BUFFER15 = 34868
GL_BLEND_EQUATION_ALPHA = 34877
GL_TEXTURE_DEPTH_SIZE = 34890
GL_DEPTH_TEXTURE_MODE = 34891
GL_TEXTURE_COMPARE_MODE = 34892
GL_TEXTURE_COMPARE_FUNC = 34893
GL_COMPARE_R_TO_TEXTURE = 34894
GL_COMPARE_REF_TO_TEXTURE = 34894
GL_TEXTURE_CUBE_MAP_SEAMLESS = 34895
GL_POINT_SPRITE = 34913
GL_COORD_REPLACE = 34914
GL_QUERY_COUNTER_BITS = 34916
GL_CURRENT_QUERY = 34917
GL_QUERY_RESULT = 34918
GL_QUERY_RESULT_AVAILABLE = 34919
GL_MAX_VERTEX_ATTRIBS = 34921
GL_VERTEX_ATTRIB_ARRAY_NORMALIZED = 34922
GL_MAX_TESS_CONTROL_INPUT_COMPONENTS = 34924
GL_MAX_TESS_EVALUATION_INPUT_COMPONENTS = 34925
GL_MAX_TEXTURE_COORDS = 34929
GL_MAX_TEXTURE_IMAGE_UNITS = 34930
GL_GEOMETRY_SHADER_INVOCATIONS = 34943
GL_ARRAY_BUFFER = 34962
GL_ELEMENT_ARRAY_BUFFER = 34963
GL_ARRAY_BUFFER_BINDING = 34964
GL_ELEMENT_ARRAY_BUFFER_BINDING = 34965
GL_VERTEX_ARRAY_BUFFER_BINDING = 34966
GL_NORMAL_ARRAY_BUFFER_BINDING = 34967
GL_COLOR_ARRAY_BUFFER_BINDING = 34968
GL_INDEX_ARRAY_BUFFER_BINDING = 34969
GL_TEXTURE_COORD_ARRAY_BUFFER_BINDING = 34970
GL_EDGE_FLAG_ARRAY_BUFFER_BINDING = 34971
GL_SECONDARY_COLOR_ARRAY_BUFFER_BINDING = 34972
GL_FOG_COORDINATE_ARRAY_BUFFER_BINDING = 34973
GL_FOG_COORD_ARRAY_BUFFER_BINDING = 34973
GL_WEIGHT_ARRAY_BUFFER_BINDING = 34974
GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING = 34975
GL_READ_ONLY = 35000
GL_WRITE_ONLY = 35001
GL_READ_WRITE = 35002
GL_BUFFER_ACCESS = 35003
GL_BUFFER_MAPPED = 35004
GL_BUFFER_MAP_POINTER = 35005
GL_TIME_ELAPSED = 35007
GL_STREAM_DRAW = 35040
GL_STREAM_READ = 35041
GL_STREAM_COPY = 35042
GL_STATIC_DRAW = 35044
GL_STATIC_READ = 35045
GL_STATIC_COPY = 35046
GL_DYNAMIC_DRAW = 35048
GL_DYNAMIC_READ = 35049
GL_DYNAMIC_COPY = 35050
GL_PIXEL_PACK_BUFFER = 35051
GL_PIXEL_UNPACK_BUFFER = 35052
GL_PIXEL_PACK_BUFFER_BINDING = 35053
GL_PIXEL_UNPACK_BUFFER_BINDING = 35055
GL_DEPTH24_STENCIL8 = 35056
GL_TEXTURE_STENCIL_SIZE = 35057
GL_SRC1_COLOR = 35065
GL_ONE_MINUS_SRC1_COLOR = 35066
GL_ONE_MINUS_SRC1_ALPHA = 35067
GL_MAX_DUAL_SOURCE_DRAW_BUFFERS = 35068
GL_VERTEX_ATTRIB_ARRAY_INTEGER = 35069
GL_VERTEX_ATTRIB_ARRAY_DIVISOR = 35070
GL_MAX_ARRAY_TEXTURE_LAYERS = 35071
GL_MIN_PROGRAM_TEXEL_OFFSET = 35076
GL_MAX_PROGRAM_TEXEL_OFFSET = 35077
GL_SAMPLES_PASSED = 35092
GL_GEOMETRY_VERTICES_OUT = 35094
GL_GEOMETRY_INPUT_TYPE = 35095
GL_GEOMETRY_OUTPUT_TYPE = 35096
GL_SAMPLER_BINDING = 35097
GL_CLAMP_VERTEX_COLOR = 35098
GL_CLAMP_FRAGMENT_COLOR = 35099
GL_CLAMP_READ_COLOR = 35100
GL_FIXED_ONLY = 35101
GL_UNIFORM_BUFFER = 35345
GL_UNIFORM_BUFFER_BINDING = 35368
GL_UNIFORM_BUFFER_START = 35369
GL_UNIFORM_BUFFER_SIZE = 35370
GL_MAX_VERTEX_UNIFORM_BLOCKS = 35371
GL_MAX_GEOMETRY_UNIFORM_BLOCKS = 35372
GL_MAX_FRAGMENT_UNIFORM_BLOCKS = 35373
GL_MAX_COMBINED_UNIFORM_BLOCKS = 35374
GL_MAX_UNIFORM_BUFFER_BINDINGS = 35375
GL_MAX_UNIFORM_BLOCK_SIZE = 35376
GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS = 35377
GL_MAX_COMBINED_GEOMETRY_UNIFORM_COMPONENTS = 35378
GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS = 35379
GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT = 35380
GL_ACTIVE_UNIFORM_BLOCK_MAX_NAME_LENGTH = 35381
GL_ACTIVE_UNIFORM_BLOCKS = 35382
GL_UNIFORM_TYPE = 35383
GL_UNIFORM_SIZE = 35384
GL_UNIFORM_NAME_LENGTH = 35385
GL_UNIFORM_BLOCK_INDEX = 35386
GL_UNIFORM_OFFSET = 35387
GL_UNIFORM_ARRAY_STRIDE = 35388
GL_UNIFORM_MATRIX_STRIDE = 35389
GL_UNIFORM_IS_ROW_MAJOR = 35390
GL_UNIFORM_BLOCK_BINDING = 35391
GL_UNIFORM_BLOCK_DATA_SIZE = 35392
GL_UNIFORM_BLOCK_NAME_LENGTH = 35393
GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS = 35394
GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES = 35395
GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER = 35396
GL_UNIFORM_BLOCK_REFERENCED_BY_GEOMETRY_SHADER = 35397
GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER = 35398
GL_FRAGMENT_SHADER = 35632
GL_VERTEX_SHADER = 35633
GL_MAX_FRAGMENT_UNIFORM_COMPONENTS = 35657
GL_MAX_VERTEX_UNIFORM_COMPONENTS = 35658
GL_MAX_VARYING_FLOATS = 35659
GL_MAX_VARYING_COMPONENTS = 35659
GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS = 35660
GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS = 35661
GL_SHADER_TYPE = 35663
GL_FLOAT_VEC2 = 35664
GL_FLOAT_VEC3 = 35665
GL_FLOAT_VEC4 = 35666
GL_INT_VEC2 = 35667
GL_INT_VEC3 = 35668
GL_INT_VEC4 = 35669
GL_BOOL = 35670
GL_BOOL_VEC2 = 35671
GL_BOOL_VEC3 = 35672
GL_BOOL_VEC4 = 35673
GL_FLOAT_MAT2 = 35674
GL_FLOAT_MAT3 = 35675
GL_FLOAT_MAT4 = 35676
GL_SAMPLER_1D = 35677
GL_SAMPLER_2D = 35678
GL_SAMPLER_3D = 35679
GL_SAMPLER_CUBE = 35680
GL_SAMPLER_1D_SHADOW = 35681
GL_SAMPLER_2D_SHADOW = 35682
GL_SAMPLER_2D_RECT = 35683
GL_SAMPLER_2D_RECT_SHADOW = 35684
GL_FLOAT_MAT2x3 = 35685
GL_FLOAT_MAT2x4 = 35686
GL_FLOAT_MAT3x2 = 35687
GL_FLOAT_MAT3x4 = 35688
GL_FLOAT_MAT4x2 = 35689
GL_FLOAT_MAT4x3 = 35690
GL_DELETE_STATUS = 35712
GL_COMPILE_STATUS = 35713
GL_LINK_STATUS = 35714
GL_VALIDATE_STATUS = 35715
GL_INFO_LOG_LENGTH = 35716
GL_ATTACHED_SHADERS = 35717
GL_ACTIVE_UNIFORMS = 35718
GL_ACTIVE_UNIFORM_MAX_LENGTH = 35719
GL_SHADER_SOURCE_LENGTH = 35720
GL_ACTIVE_ATTRIBUTES = 35721
GL_ACTIVE_ATTRIBUTE_MAX_LENGTH = 35722
GL_FRAGMENT_SHADER_DERIVATIVE_HINT = 35723
GL_SHADING_LANGUAGE_VERSION = 35724
GL_CURRENT_PROGRAM = 35725
GL_IMPLEMENTATION_COLOR_READ_TYPE = 35738
GL_IMPLEMENTATION_COLOR_READ_FORMAT = 35739
GL_TEXTURE_RED_TYPE = 35856
GL_TEXTURE_GREEN_TYPE = 35857
GL_TEXTURE_BLUE_TYPE = 35858
GL_TEXTURE_ALPHA_TYPE = 35859
GL_TEXTURE_LUMINANCE_TYPE = 35860
GL_TEXTURE_INTENSITY_TYPE = 35861
GL_TEXTURE_DEPTH_TYPE = 35862
GL_UNSIGNED_NORMALIZED = 35863
GL_TEXTURE_1D_ARRAY = 35864
GL_PROXY_TEXTURE_1D_ARRAY = 35865
GL_TEXTURE_2D_ARRAY = 35866
GL_PROXY_TEXTURE_2D_ARRAY = 35867
GL_TEXTURE_BINDING_1D_ARRAY = 35868
GL_TEXTURE_BINDING_2D_ARRAY = 35869
GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS = 35881
GL_TEXTURE_BUFFER = 35882
GL_TEXTURE_BUFFER_BINDING = 35882
GL_MAX_TEXTURE_BUFFER_SIZE = 35883
GL_TEXTURE_BINDING_BUFFER = 35884
GL_TEXTURE_BUFFER_DATA_STORE_BINDING = 35885
GL_ANY_SAMPLES_PASSED = 35887
GL_SAMPLE_SHADING = 35894
GL_MIN_SAMPLE_SHADING_VALUE = 35895
GL_R11F_G11F_B10F = 35898
GL_UNSIGNED_INT_10F_11F_11F_REV = 35899
GL_RGB9_E5 = 35901
GL_UNSIGNED_INT_5_9_9_9_REV = 35902
GL_TEXTURE_SHARED_SIZE = 35903
GL_SRGB = 35904
GL_SRGB8 = 35905
GL_SRGB_ALPHA = 35906
GL_SRGB8_ALPHA8 = 35907
GL_SLUMINANCE_ALPHA = 35908
GL_SLUMINANCE8_ALPHA8 = 35909
GL_SLUMINANCE = 35910
GL_SLUMINANCE8 = 35911
GL_COMPRESSED_SRGB = 35912
GL_COMPRESSED_SRGB_ALPHA = 35913
GL_COMPRESSED_SLUMINANCE = 35914
GL_COMPRESSED_SLUMINANCE_ALPHA = 35915
GL_TRANSFORM_FEEDBACK_VARYING_MAX_LENGTH = 35958
GL_TRANSFORM_FEEDBACK_BUFFER_MODE = 35967
GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS = 35968
GL_TRANSFORM_FEEDBACK_VARYINGS = 35971
GL_TRANSFORM_FEEDBACK_BUFFER_START = 35972
GL_TRANSFORM_FEEDBACK_BUFFER_SIZE = 35973
GL_PRIMITIVES_GENERATED = 35975
GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN = 35976
GL_RASTERIZER_DISCARD = 35977
GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS = 35978
GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS = 35979
GL_INTERLEAVED_ATTRIBS = 35980
GL_SEPARATE_ATTRIBS = 35981
GL_TRANSFORM_FEEDBACK_BUFFER = 35982
GL_TRANSFORM_FEEDBACK_BUFFER_BINDING = 35983
GL_POINT_SPRITE_COORD_ORIGIN = 36000
GL_LOWER_LEFT = 36001
GL_UPPER_LEFT = 36002
GL_STENCIL_BACK_REF = 36003
GL_STENCIL_BACK_VALUE_MASK = 36004
GL_STENCIL_BACK_WRITEMASK = 36005
GL_FRAMEBUFFER_BINDING = 36006
GL_DRAW_FRAMEBUFFER_BINDING = 36006
GL_FRAMEBUFFER_BINDING_EXT = 36006
GL_RENDERBUFFER_BINDING = 36007
GL_RENDERBUFFER_BINDING_EXT = 36007
GL_READ_FRAMEBUFFER = 36008
GL_DRAW_FRAMEBUFFER = 36009
GL_READ_FRAMEBUFFER_BINDING = 36010
GL_RENDERBUFFER_SAMPLES = 36011
GL_DEPTH_COMPONENT32F = 36012
GL_DEPTH32F_STENCIL8 = 36013
GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE = 36048
GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE_EXT = 36048
GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME = 36049
GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME_EXT = 36049
GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL = 36050
GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL_EXT = 36050
GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE = 36051
GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE_EXT = 36051
GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER = 36052
GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_3D_ZOFFSET_EXT = 36052
GL_FRAMEBUFFER_COMPLETE = 36053
GL_FRAMEBUFFER_COMPLETE_EXT = 36053
GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT = 36054
GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT_EXT = 36054
GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT = 36055
GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT_EXT = 36055
GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS_EXT = 36057
GL_FRAMEBUFFER_INCOMPLETE_FORMATS_EXT = 36058
GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER = 36059
GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER_EXT = 36059
GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER = 36060
GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER_EXT = 36060
GL_FRAMEBUFFER_UNSUPPORTED = 36061
GL_FRAMEBUFFER_UNSUPPORTED_EXT = 36061
GL_MAX_COLOR_ATTACHMENTS = 36063
GL_MAX_COLOR_ATTACHMENTS_EXT = 36063
GL_COLOR_ATTACHMENT0 = 36064
GL_COLOR_ATTACHMENT0_EXT = 36064
GL_COLOR_ATTACHMENT1 = 36065
GL_COLOR_ATTACHMENT1_EXT = 36065
GL_COLOR_ATTACHMENT2 = 36066
GL_COLOR_ATTACHMENT2_EXT = 36066
GL_COLOR_ATTACHMENT3 = 36067
GL_COLOR_ATTACHMENT3_EXT = 36067
GL_COLOR_ATTACHMENT4 = 36068
GL_COLOR_ATTACHMENT4_EXT = 36068
GL_COLOR_ATTACHMENT5 = 36069
GL_COLOR_ATTACHMENT5_EXT = 36069
GL_COLOR_ATTACHMENT6 = 36070
GL_COLOR_ATTACHMENT6_EXT = 36070
GL_COLOR_ATTACHMENT7 = 36071
GL_COLOR_ATTACHMENT7_EXT = 36071
GL_COLOR_ATTACHMENT8 = 36072
GL_COLOR_ATTACHMENT8_EXT = 36072
GL_COLOR_ATTACHMENT9 = 36073
GL_COLOR_ATTACHMENT9_EXT = 36073
GL_COLOR_ATTACHMENT10 = 36074
GL_COLOR_ATTACHMENT10_EXT = 36074
GL_COLOR_ATTACHMENT11 = 36075
GL_COLOR_ATTACHMENT11_EXT = 36075
GL_COLOR_ATTACHMENT12 = 36076
GL_COLOR_ATTACHMENT12_EXT = 36076
GL_COLOR_ATTACHMENT13 = 36077
GL_COLOR_ATTACHMENT13_EXT = 36077
GL_COLOR_ATTACHMENT14 = 36078
GL_COLOR_ATTACHMENT14_EXT = 36078
GL_COLOR_ATTACHMENT15 = 36079
GL_COLOR_ATTACHMENT15_EXT = 36079
GL_COLOR_ATTACHMENT16 = 36080
GL_COLOR_ATTACHMENT17 = 36081
GL_COLOR_ATTACHMENT18 = 36082
GL_COLOR_ATTACHMENT19 = 36083
GL_COLOR_ATTACHMENT20 = 36084
GL_COLOR_ATTACHMENT21 = 36085
GL_COLOR_ATTACHMENT22 = 36086
GL_COLOR_ATTACHMENT23 = 36087
GL_COLOR_ATTACHMENT24 = 36088
GL_COLOR_ATTACHMENT25 = 36089
GL_COLOR_ATTACHMENT26 = 36090
GL_COLOR_ATTACHMENT27 = 36091
GL_COLOR_ATTACHMENT28 = 36092
GL_COLOR_ATTACHMENT29 = 36093
GL_COLOR_ATTACHMENT30 = 36094
GL_COLOR_ATTACHMENT31 = 36095
GL_DEPTH_ATTACHMENT = 36096
GL_DEPTH_ATTACHMENT_EXT = 36096
GL_STENCIL_ATTACHMENT = 36128
GL_STENCIL_ATTACHMENT_EXT = 36128
GL_FRAMEBUFFER = 36160
GL_FRAMEBUFFER_EXT = 36160
GL_RENDERBUFFER = 36161
GL_RENDERBUFFER_EXT = 36161
GL_RENDERBUFFER_WIDTH = 36162
GL_RENDERBUFFER_WIDTH_EXT = 36162
GL_RENDERBUFFER_HEIGHT = 36163
GL_RENDERBUFFER_HEIGHT_EXT = 36163
GL_RENDERBUFFER_INTERNAL_FORMAT = 36164
GL_RENDERBUFFER_INTERNAL_FORMAT_EXT = 36164
GL_STENCIL_INDEX1 = 36166
GL_STENCIL_INDEX1_EXT = 36166
GL_STENCIL_INDEX4 = 36167
GL_STENCIL_INDEX4_EXT = 36167
GL_STENCIL_INDEX8 = 36168
GL_STENCIL_INDEX8_EXT = 36168
GL_STENCIL_INDEX16 = 36169
GL_STENCIL_INDEX16_EXT = 36169
GL_RENDERBUFFER_RED_SIZE = 36176
GL_RENDERBUFFER_RED_SIZE_EXT = 36176
GL_RENDERBUFFER_GREEN_SIZE = 36177
GL_RENDERBUFFER_GREEN_SIZE_EXT = 36177
GL_RENDERBUFFER_BLUE_SIZE = 36178
GL_RENDERBUFFER_BLUE_SIZE_EXT = 36178
GL_RENDERBUFFER_ALPHA_SIZE = 36179
GL_RENDERBUFFER_ALPHA_SIZE_EXT = 36179
GL_RENDERBUFFER_DEPTH_SIZE = 36180
GL_RENDERBUFFER_DEPTH_SIZE_EXT = 36180
GL_RENDERBUFFER_STENCIL_SIZE = 36181
GL_RENDERBUFFER_STENCIL_SIZE_EXT = 36181
GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE = 36182
GL_MAX_SAMPLES = 36183
GL_RGB565 = 36194
GL_PRIMITIVE_RESTART_FIXED_INDEX = 36201
GL_ANY_SAMPLES_PASSED_CONSERVATIVE = 36202
GL_MAX_ELEMENT_INDEX = 36203
GL_RGBA32UI = 36208
GL_RGB32UI = 36209
GL_RGBA16UI = 36214
GL_RGB16UI = 36215
GL_RGBA8UI = 36220
GL_RGB8UI = 36221
GL_RGBA32I = 36226
GL_RGB32I = 36227
GL_RGBA16I = 36232
GL_RGB16I = 36233
GL_RGBA8I = 36238
GL_RGB8I = 36239
GL_RED_INTEGER = 36244
GL_GREEN_INTEGER = 36245
GL_BLUE_INTEGER = 36246
GL_ALPHA_INTEGER = 36247
GL_RGB_INTEGER = 36248
GL_RGBA_INTEGER = 36249
GL_BGR_INTEGER = 36250
GL_BGRA_INTEGER = 36251
GL_INT_2_10_10_10_REV = 36255
GL_FRAMEBUFFER_ATTACHMENT_LAYERED = 36263
GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS = 36264
GL_FLOAT_32_UNSIGNED_INT_24_8_REV = 36269
GL_FRAMEBUFFER_SRGB = 36281
GL_COMPRESSED_RED_RGTC1 = 36283
GL_COMPRESSED_SIGNED_RED_RGTC1 = 36284
GL_COMPRESSED_RG_RGTC2 = 36285
GL_COMPRESSED_SIGNED_RG_RGTC2 = 36286
GL_SAMPLER_1D_ARRAY = 36288
GL_SAMPLER_2D_ARRAY = 36289
GL_SAMPLER_BUFFER = 36290
GL_SAMPLER_1D_ARRAY_SHADOW = 36291
GL_SAMPLER_2D_ARRAY_SHADOW = 36292
GL_SAMPLER_CUBE_SHADOW = 36293
GL_UNSIGNED_INT_VEC2 = 36294
GL_UNSIGNED_INT_VEC3 = 36295
GL_UNSIGNED_INT_VEC4 = 36296
GL_INT_SAMPLER_1D = 36297
GL_INT_SAMPLER_2D = 36298
GL_INT_SAMPLER_3D = 36299
GL_INT_SAMPLER_CUBE = 36300
GL_INT_SAMPLER_2D_RECT = 36301
GL_INT_SAMPLER_1D_ARRAY = 36302
GL_INT_SAMPLER_2D_ARRAY = 36303
GL_INT_SAMPLER_BUFFER = 36304
GL_UNSIGNED_INT_SAMPLER_1D = 36305
GL_UNSIGNED_INT_SAMPLER_2D = 36306
GL_UNSIGNED_INT_SAMPLER_3D = 36307
GL_UNSIGNED_INT_SAMPLER_CUBE = 36308
GL_UNSIGNED_INT_SAMPLER_2D_RECT = 36309
GL_UNSIGNED_INT_SAMPLER_1D_ARRAY = 36310
GL_UNSIGNED_INT_SAMPLER_2D_ARRAY = 36311
GL_UNSIGNED_INT_SAMPLER_BUFFER = 36312
GL_GEOMETRY_SHADER = 36313
GL_MAX_GEOMETRY_UNIFORM_COMPONENTS = 36319
GL_MAX_GEOMETRY_OUTPUT_VERTICES = 36320
GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS = 36321
GL_ACTIVE_SUBROUTINES = 36325
GL_ACTIVE_SUBROUTINE_UNIFORMS = 36326
GL_MAX_SUBROUTINES = 36327
GL_MAX_SUBROUTINE_UNIFORM_LOCATIONS = 36328
GL_LOW_FLOAT = 36336
GL_MEDIUM_FLOAT = 36337
GL_HIGH_FLOAT = 36338
GL_LOW_INT = 36339
GL_MEDIUM_INT = 36340
GL_HIGH_INT = 36341
GL_SHADER_BINARY_FORMATS = 36344
GL_NUM_SHADER_BINARY_FORMATS = 36345
GL_SHADER_COMPILER = 36346
GL_MAX_VERTEX_UNIFORM_VECTORS = 36347
GL_MAX_VARYING_VECTORS = 36348
GL_MAX_FRAGMENT_UNIFORM_VECTORS = 36349
GL_QUERY_WAIT = 36371
GL_QUERY_NO_WAIT = 36372
GL_QUERY_BY_REGION_WAIT = 36373
GL_QUERY_BY_REGION_NO_WAIT = 36374
GL_QUERY_WAIT_INVERTED = 36375
GL_QUERY_NO_WAIT_INVERTED = 36376
GL_QUERY_BY_REGION_WAIT_INVERTED = 36377
GL_QUERY_BY_REGION_NO_WAIT_INVERTED = 36378
GL_POLYGON_OFFSET_CLAMP = 36379
GL_MAX_COMBINED_TESS_CONTROL_UNIFORM_COMPONENTS = 36382
GL_MAX_COMBINED_TESS_EVALUATION_UNIFORM_COMPONENTS = 36383
GL_TRANSFORM_FEEDBACK = 36386
GL_TRANSFORM_FEEDBACK_BUFFER_PAUSED = 36387
GL_TRANSFORM_FEEDBACK_PAUSED = 36387
GL_TRANSFORM_FEEDBACK_BUFFER_ACTIVE = 36388
GL_TRANSFORM_FEEDBACK_ACTIVE = 36388
GL_TRANSFORM_FEEDBACK_BINDING = 36389
GL_TIMESTAMP = 36392
GL_TEXTURE_SWIZZLE_R = 36418
GL_TEXTURE_SWIZZLE_G = 36419
GL_TEXTURE_SWIZZLE_B = 36420
GL_TEXTURE_SWIZZLE_A = 36421
GL_TEXTURE_SWIZZLE_RGBA = 36422
GL_ACTIVE_SUBROUTINE_UNIFORM_LOCATIONS = 36423
GL_ACTIVE_SUBROUTINE_MAX_LENGTH = 36424
GL_ACTIVE_SUBROUTINE_UNIFORM_MAX_LENGTH = 36425
GL_NUM_COMPATIBLE_SUBROUTINES = 36426
GL_COMPATIBLE_SUBROUTINES = 36427
GL_QUADS_FOLLOW_PROVOKING_VERTEX_CONVENTION = 36428
GL_FIRST_VERTEX_CONVENTION = 36429
GL_LAST_VERTEX_CONVENTION = 36430
GL_PROVOKING_VERTEX = 36431
GL_SAMPLE_POSITION = 36432
GL_SAMPLE_MASK = 36433
GL_SAMPLE_MASK_VALUE = 36434
GL_MAX_SAMPLE_MASK_WORDS = 36441
GL_MAX_GEOMETRY_SHADER_INVOCATIONS = 36442
GL_MIN_FRAGMENT_INTERPOLATION_OFFSET = 36443
GL_MAX_FRAGMENT_INTERPOLATION_OFFSET = 36444
GL_FRAGMENT_INTERPOLATION_OFFSET_BITS = 36445
GL_MIN_PROGRAM_TEXTURE_GATHER_OFFSET = 36446
GL_MAX_PROGRAM_TEXTURE_GATHER_OFFSET = 36447
GL_MAX_MESH_UNIFORM_BLOCKS_NV = 36448
GL_MAX_MESH_TEXTURE_IMAGE_UNITS_NV = 36449
GL_MAX_MESH_IMAGE_UNIFORMS_NV = 36450
GL_MAX_MESH_UNIFORM_COMPONENTS_NV = 36451
GL_MAX_MESH_ATOMIC_COUNTER_BUFFERS_NV = 36452
GL_MAX_MESH_ATOMIC_COUNTERS_NV = 36453
GL_MAX_MESH_SHADER_STORAGE_BLOCKS_NV = 36454
GL_MAX_COMBINED_MESH_UNIFORM_COMPONENTS_NV = 36455
GL_MAX_TASK_UNIFORM_BLOCKS_NV = 36456
GL_MAX_TASK_TEXTURE_IMAGE_UNITS_NV = 36457
GL_MAX_TASK_IMAGE_UNIFORMS_NV = 36458
GL_MAX_TASK_UNIFORM_COMPONENTS_NV = 36459
GL_MAX_TASK_ATOMIC_COUNTER_BUFFERS_NV = 36460
GL_MAX_TASK_ATOMIC_COUNTERS_NV = 36461
GL_MAX_TASK_SHADER_STORAGE_BLOCKS_NV = 36462
GL_MAX_COMBINED_TASK_UNIFORM_COMPONENTS_NV = 36463
GL_MAX_TRANSFORM_FEEDBACK_BUFFERS = 36464
GL_MAX_VERTEX_STREAMS = 36465
GL_PATCH_VERTICES = 36466
GL_PATCH_DEFAULT_INNER_LEVEL = 36467
GL_PATCH_DEFAULT_OUTER_LEVEL = 36468
GL_TESS_CONTROL_OUTPUT_VERTICES = 36469
GL_TESS_GEN_MODE = 36470
GL_TESS_GEN_SPACING = 36471
GL_TESS_GEN_VERTEX_ORDER = 36472
GL_TESS_GEN_POINT_MODE = 36473
GL_ISOLINES = 36474
GL_FRACTIONAL_ODD = 36475
GL_FRACTIONAL_EVEN = 36476
GL_MAX_PATCH_VERTICES = 36477
GL_MAX_TESS_GEN_LEVEL = 36478
GL_MAX_TESS_CONTROL_UNIFORM_COMPONENTS = 36479
GL_MAX_TESS_EVALUATION_UNIFORM_COMPONENTS = 36480
GL_MAX_TESS_CONTROL_TEXTURE_IMAGE_UNITS = 36481
GL_MAX_TESS_EVALUATION_TEXTURE_IMAGE_UNITS = 36482
GL_MAX_TESS_CONTROL_OUTPUT_COMPONENTS = 36483
GL_MAX_TESS_PATCH_COMPONENTS = 36484
GL_MAX_TESS_CONTROL_TOTAL_OUTPUT_COMPONENTS = 36485
GL_MAX_TESS_EVALUATION_OUTPUT_COMPONENTS = 36486
GL_TESS_EVALUATION_SHADER = 36487
GL_TESS_CONTROL_SHADER = 36488
GL_MAX_TESS_CONTROL_UNIFORM_BLOCKS = 36489
GL_MAX_TESS_EVALUATION_UNIFORM_BLOCKS = 36490
GL_COMPRESSED_RGBA_BPTC_UNORM = 36492
GL_COMPRESSED_SRGB_ALPHA_BPTC_UNORM = 36493
GL_COMPRESSED_RGB_BPTC_SIGNED_FLOAT = 36494
GL_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT = 36495
GL_COPY_READ_BUFFER = 36662
GL_COPY_READ_BUFFER_BINDING = 36662
GL_COPY_WRITE_BUFFER = 36663
GL_COPY_WRITE_BUFFER_BINDING = 36663
GL_MAX_IMAGE_UNITS = 36664
GL_MAX_COMBINED_IMAGE_UNITS_AND_FRAGMENT_OUTPUTS = 36665
GL_MAX_COMBINED_SHADER_OUTPUT_RESOURCES = 36665
GL_IMAGE_BINDING_NAME = 36666
GL_IMAGE_BINDING_LEVEL = 36667
GL_IMAGE_BINDING_LAYERED = 36668
GL_IMAGE_BINDING_LAYER = 36669
GL_IMAGE_BINDING_ACCESS = 36670
GL_DRAW_INDIRECT_BUFFER = 36671
GL_DRAW_INDIRECT_BUFFER_BINDING = 36675
GL_DOUBLE_MAT2 = 36678
GL_DOUBLE_MAT3 = 36679
GL_DOUBLE_MAT4 = 36680
GL_DOUBLE_MAT2x3 = 36681
GL_DOUBLE_MAT2x4 = 36682
GL_DOUBLE_MAT3x2 = 36683
GL_DOUBLE_MAT3x4 = 36684
GL_DOUBLE_MAT4x2 = 36685
GL_DOUBLE_MAT4x3 = 36686
GL_VERTEX_BINDING_BUFFER = 36687
GL_R8_SNORM = 36756
GL_RG8_SNORM = 36757
GL_RGB8_SNORM = 36758
GL_RGBA8_SNORM = 36759
GL_R16_SNORM = 36760
GL_RG16_SNORM = 36761
GL_RGB16_SNORM = 36762
GL_RGBA16_SNORM = 36763
GL_SIGNED_NORMALIZED = 36764
GL_PRIMITIVE_RESTART = 36765
GL_PRIMITIVE_RESTART_INDEX = 36766
GL_INT64_VEC2_ARB = 36841
GL_INT64_VEC3_ARB = 36842
GL_INT64_VEC4_ARB = 36843
GL_UNSIGNED_INT64_VEC2_ARB = 36853
GL_UNSIGNED_INT64_VEC3_ARB = 36854
GL_UNSIGNED_INT64_VEC4_ARB = 36855
GL_DOUBLE_VEC2 = 36860
GL_DOUBLE_VEC3 = 36861
GL_DOUBLE_VEC4 = 36862
GL_TEXTURE_CUBE_MAP_ARRAY = 36873
GL_TEXTURE_BINDING_CUBE_MAP_ARRAY = 36874
GL_PROXY_TEXTURE_CUBE_MAP_ARRAY = 36875
GL_SAMPLER_CUBE_MAP_ARRAY = 36876
GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW = 36877
GL_INT_SAMPLER_CUBE_MAP_ARRAY = 36878
GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY = 36879
GL_IMAGE_1D = 36940
GL_IMAGE_2D = 36941
GL_IMAGE_3D = 36942
GL_IMAGE_2D_RECT = 36943
GL_IMAGE_CUBE = 36944
GL_IMAGE_BUFFER = 36945
GL_IMAGE_1D_ARRAY = 36946
GL_IMAGE_2D_ARRAY = 36947
GL_IMAGE_CUBE_MAP_ARRAY = 36948
GL_IMAGE_2D_MULTISAMPLE = 36949
GL_IMAGE_2D_MULTISAMPLE_ARRAY = 36950
GL_INT_IMAGE_1D = 36951
GL_INT_IMAGE_2D = 36952
GL_INT_IMAGE_3D = 36953
GL_INT_IMAGE_2D_RECT = 36954
GL_INT_IMAGE_CUBE = 36955
GL_INT_IMAGE_BUFFER = 36956
GL_INT_IMAGE_1D_ARRAY = 36957
GL_INT_IMAGE_2D_ARRAY = 36958
GL_INT_IMAGE_CUBE_MAP_ARRAY = 36959
GL_INT_IMAGE_2D_MULTISAMPLE = 36960
GL_INT_IMAGE_2D_MULTISAMPLE_ARRAY = 36961
GL_UNSIGNED_INT_IMAGE_1D = 36962
GL_UNSIGNED_INT_IMAGE_2D = 36963
GL_UNSIGNED_INT_IMAGE_3D = 36964
GL_UNSIGNED_INT_IMAGE_2D_RECT = 36965
GL_UNSIGNED_INT_IMAGE_CUBE = 36966
GL_UNSIGNED_INT_IMAGE_BUFFER = 36967
GL_UNSIGNED_INT_IMAGE_1D_ARRAY = 36968
GL_UNSIGNED_INT_IMAGE_2D_ARRAY = 36969
GL_UNSIGNED_INT_IMAGE_CUBE_MAP_ARRAY = 36970
GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE = 36971
GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE_ARRAY = 36972
GL_MAX_IMAGE_SAMPLES = 36973
GL_IMAGE_BINDING_FORMAT = 36974
GL_RGB10_A2UI = 36975
GL_MIN_MAP_BUFFER_ALIGNMENT = 37052
GL_IMAGE_FORMAT_COMPATIBILITY_TYPE = 37063
GL_IMAGE_FORMAT_COMPATIBILITY_BY_SIZE = 37064
GL_IMAGE_FORMAT_COMPATIBILITY_BY_CLASS = 37065
GL_MAX_VERTEX_IMAGE_UNIFORMS = 37066
GL_MAX_TESS_CONTROL_IMAGE_UNIFORMS = 37067
GL_MAX_TESS_EVALUATION_IMAGE_UNIFORMS = 37068
GL_MAX_GEOMETRY_IMAGE_UNIFORMS = 37069
GL_MAX_FRAGMENT_IMAGE_UNIFORMS = 37070
GL_MAX_COMBINED_IMAGE_UNIFORMS = 37071
GL_SHADER_STORAGE_BUFFER = 37074
GL_SHADER_STORAGE_BUFFER_BINDING = 37075
GL_SHADER_STORAGE_BUFFER_START = 37076
GL_SHADER_STORAGE_BUFFER_SIZE = 37077
GL_MAX_VERTEX_SHADER_STORAGE_BLOCKS = 37078
GL_MAX_GEOMETRY_SHADER_STORAGE_BLOCKS = 37079
GL_MAX_TESS_CONTROL_SHADER_STORAGE_BLOCKS = 37080
GL_MAX_TESS_EVALUATION_SHADER_STORAGE_BLOCKS = 37081
GL_MAX_FRAGMENT_SHADER_STORAGE_BLOCKS = 37082
GL_MAX_COMPUTE_SHADER_STORAGE_BLOCKS = 37083
GL_MAX_COMBINED_SHADER_STORAGE_BLOCKS = 37084
GL_MAX_SHADER_STORAGE_BUFFER_BINDINGS = 37085
GL_MAX_SHADER_STORAGE_BLOCK_SIZE = 37086
GL_SHADER_STORAGE_BUFFER_OFFSET_ALIGNMENT = 37087
GL_DEPTH_STENCIL_TEXTURE_MODE = 37098
GL_MAX_COMPUTE_WORK_GROUP_INVOCATIONS = 37099
GL_UNIFORM_BLOCK_REFERENCED_BY_COMPUTE_SHADER = 37100
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_COMPUTE_SHADER = 37101
GL_DISPATCH_INDIRECT_BUFFER = 37102
GL_DISPATCH_INDIRECT_BUFFER_BINDING = 37103
GL_TEXTURE_2D_MULTISAMPLE = 37120
GL_PROXY_TEXTURE_2D_MULTISAMPLE = 37121
GL_TEXTURE_2D_MULTISAMPLE_ARRAY = 37122
GL_PROXY_TEXTURE_2D_MULTISAMPLE_ARRAY = 37123
GL_TEXTURE_BINDING_2D_MULTISAMPLE = 37124
GL_TEXTURE_BINDING_2D_MULTISAMPLE_ARRAY = 37125
GL_TEXTURE_SAMPLES = 37126
GL_TEXTURE_FIXED_SAMPLE_LOCATIONS = 37127
GL_SAMPLER_2D_MULTISAMPLE = 37128
GL_INT_SAMPLER_2D_MULTISAMPLE = 37129
GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE = 37130
GL_SAMPLER_2D_MULTISAMPLE_ARRAY = 37131
GL_INT_SAMPLER_2D_MULTISAMPLE_ARRAY = 37132
GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE_ARRAY = 37133
GL_MAX_COLOR_TEXTURE_SAMPLES = 37134
GL_MAX_DEPTH_TEXTURE_SAMPLES = 37135
GL_MAX_INTEGER_SAMPLES = 37136
GL_MAX_SERVER_WAIT_TIMEOUT = 37137
GL_OBJECT_TYPE = 37138
GL_SYNC_CONDITION = 37139
GL_SYNC_STATUS = 37140
GL_SYNC_FLAGS = 37141
GL_SYNC_FENCE = 37142
GL_SYNC_GPU_COMMANDS_COMPLETE = 37143
GL_UNSIGNALED = 37144
GL_SIGNALED = 37145
GL_ALREADY_SIGNALED = 37146
GL_TIMEOUT_EXPIRED = 37147
GL_CONDITION_SATISFIED = 37148
GL_WAIT_FAILED = 37149
GL_BUFFER_ACCESS_FLAGS = 37151
GL_BUFFER_MAP_LENGTH = 37152
GL_BUFFER_MAP_OFFSET = 37153
GL_MAX_VERTEX_OUTPUT_COMPONENTS = 37154
GL_MAX_GEOMETRY_INPUT_COMPONENTS = 37155
GL_MAX_GEOMETRY_OUTPUT_COMPONENTS = 37156
GL_MAX_FRAGMENT_INPUT_COMPONENTS = 37157
GL_CONTEXT_PROFILE_MASK = 37158
GL_UNPACK_COMPRESSED_BLOCK_WIDTH = 37159
GL_UNPACK_COMPRESSED_BLOCK_HEIGHT = 37160
GL_UNPACK_COMPRESSED_BLOCK_DEPTH = 37161
GL_UNPACK_COMPRESSED_BLOCK_SIZE = 37162
GL_PACK_COMPRESSED_BLOCK_WIDTH = 37163
GL_PACK_COMPRESSED_BLOCK_HEIGHT = 37164
GL_PACK_COMPRESSED_BLOCK_DEPTH = 37165
GL_PACK_COMPRESSED_BLOCK_SIZE = 37166
GL_TEXTURE_IMMUTABLE_FORMAT = 37167
GL_MAX_DEBUG_MESSAGE_LENGTH = 37187
GL_MAX_DEBUG_LOGGED_MESSAGES = 37188
GL_DEBUG_LOGGED_MESSAGES = 37189
GL_DEBUG_SEVERITY_HIGH = 37190
GL_DEBUG_SEVERITY_MEDIUM = 37191
GL_DEBUG_SEVERITY_LOW = 37192
GL_QUERY_BUFFER = 37266
GL_QUERY_BUFFER_BINDING = 37267
GL_QUERY_RESULT_NO_WAIT = 37268
GL_TEXTURE_BUFFER_OFFSET = 37277
GL_TEXTURE_BUFFER_SIZE = 37278
GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT = 37279
GL_COMPUTE_SHADER = 37305
GL_MAX_COMPUTE_UNIFORM_BLOCKS = 37307
GL_MAX_COMPUTE_TEXTURE_IMAGE_UNITS = 37308
GL_MAX_COMPUTE_IMAGE_UNIFORMS = 37309
GL_MAX_COMPUTE_WORK_GROUP_COUNT = 37310
GL_MAX_COMPUTE_WORK_GROUP_SIZE = 37311
GL_COMPRESSED_R11_EAC = 37488
GL_COMPRESSED_SIGNED_R11_EAC = 37489
GL_COMPRESSED_RG11_EAC = 37490
GL_COMPRESSED_SIGNED_RG11_EAC = 37491
GL_COMPRESSED_RGB8_ETC2 = 37492
GL_COMPRESSED_SRGB8_ETC2 = 37493
GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 37494
GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 37495
GL_COMPRESSED_RGBA8_ETC2_EAC = 37496
GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 37497
GL_ATOMIC_COUNTER_BUFFER = 37568
GL_ATOMIC_COUNTER_BUFFER_BINDING = 37569
GL_ATOMIC_COUNTER_BUFFER_START = 37570
GL_ATOMIC_COUNTER_BUFFER_SIZE = 37571
GL_ATOMIC_COUNTER_BUFFER_DATA_SIZE = 37572
GL_ATOMIC_COUNTER_BUFFER_ACTIVE_ATOMIC_COUNTERS = 37573
GL_ATOMIC_COUNTER_BUFFER_ACTIVE_ATOMIC_COUNTER_INDICES = 37574
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_VERTEX_SHADER = 37575
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TESS_CONTROL_SHADER = 37576
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TESS_EVALUATION_SHADER = 37577
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_GEOMETRY_SHADER = 37578
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_FRAGMENT_SHADER = 37579
GL_MAX_VERTEX_ATOMIC_COUNTER_BUFFERS = 37580
GL_MAX_TESS_CONTROL_ATOMIC_COUNTER_BUFFERS = 37581
GL_MAX_TESS_EVALUATION_ATOMIC_COUNTER_BUFFERS = 37582
GL_MAX_GEOMETRY_ATOMIC_COUNTER_BUFFERS = 37583
GL_MAX_FRAGMENT_ATOMIC_COUNTER_BUFFERS = 37584
GL_MAX_COMBINED_ATOMIC_COUNTER_BUFFERS = 37585
GL_MAX_VERTEX_ATOMIC_COUNTERS = 37586
GL_MAX_TESS_CONTROL_ATOMIC_COUNTERS = 37587
GL_MAX_TESS_EVALUATION_ATOMIC_COUNTERS = 37588
GL_MAX_GEOMETRY_ATOMIC_COUNTERS = 37589
GL_MAX_FRAGMENT_ATOMIC_COUNTERS = 37590
GL_MAX_COMBINED_ATOMIC_COUNTERS = 37591
GL_MAX_ATOMIC_COUNTER_BUFFER_SIZE = 37592
GL_ACTIVE_ATOMIC_COUNTER_BUFFERS = 37593
GL_UNIFORM_ATOMIC_COUNTER_BUFFER_INDEX = 37594
GL_UNSIGNED_INT_ATOMIC_COUNTER = 37595
GL_MAX_ATOMIC_COUNTER_BUFFER_BINDINGS = 37596
GL_MESH_OUTPUT_PER_VERTEX_GRANULARITY_NV = 37599
GL_DEBUG_OUTPUT = 37600
GL_UNIFORM = 37601
GL_UNIFORM_BLOCK = 37602
GL_PROGRAM_INPUT = 37603
GL_PROGRAM_OUTPUT = 37604
GL_BUFFER_VARIABLE = 37605
GL_SHADER_STORAGE_BLOCK = 37606
GL_IS_PER_PATCH = 37607
GL_VERTEX_SUBROUTINE = 37608
GL_TESS_CONTROL_SUBROUTINE = 37609
GL_TESS_EVALUATION_SUBROUTINE = 37610
GL_GEOMETRY_SUBROUTINE = 37611
GL_FRAGMENT_SUBROUTINE = 37612
GL_COMPUTE_SUBROUTINE = 37613
GL_VERTEX_SUBROUTINE_UNIFORM = 37614
GL_TESS_CONTROL_SUBROUTINE_UNIFORM = 37615
GL_TESS_EVALUATION_SUBROUTINE_UNIFORM = 37616
GL_GEOMETRY_SUBROUTINE_UNIFORM = 37617
GL_FRAGMENT_SUBROUTINE_UNIFORM = 37618
GL_COMPUTE_SUBROUTINE_UNIFORM = 37619
GL_TRANSFORM_FEEDBACK_VARYING = 37620
GL_ACTIVE_RESOURCES = 37621
GL_MAX_NAME_LENGTH = 37622
GL_MAX_NUM_ACTIVE_VARIABLES = 37623
GL_MAX_NUM_COMPATIBLE_SUBROUTINES = 37624
GL_NAME_LENGTH = 37625
GL_TYPE = 37626
GL_ARRAY_SIZE = 37627
GL_OFFSET = 37628
GL_BLOCK_INDEX = 37629
GL_ARRAY_STRIDE = 37630
GL_MATRIX_STRIDE = 37631
GL_IS_ROW_MAJOR = 37632
GL_ATOMIC_COUNTER_BUFFER_INDEX = 37633
GL_BUFFER_BINDING = 37634
GL_BUFFER_DATA_SIZE = 37635
GL_NUM_ACTIVE_VARIABLES = 37636
GL_ACTIVE_VARIABLES = 37637
GL_REFERENCED_BY_VERTEX_SHADER = 37638
GL_REFERENCED_BY_TESS_CONTROL_SHADER = 37639
GL_REFERENCED_BY_TESS_EVALUATION_SHADER = 37640
GL_REFERENCED_BY_GEOMETRY_SHADER = 37641
GL_REFERENCED_BY_FRAGMENT_SHADER = 37642
GL_REFERENCED_BY_COMPUTE_SHADER = 37643
GL_TOP_LEVEL_ARRAY_SIZE = 37644
GL_TOP_LEVEL_ARRAY_STRIDE = 37645
GL_LOCATION = 37646
GL_LOCATION_INDEX = 37647
GL_FRAMEBUFFER_DEFAULT_WIDTH = 37648
GL_FRAMEBUFFER_DEFAULT_HEIGHT = 37649
GL_FRAMEBUFFER_DEFAULT_LAYERS = 37650
GL_FRAMEBUFFER_DEFAULT_SAMPLES = 37651
GL_FRAMEBUFFER_DEFAULT_FIXED_SAMPLE_LOCATIONS = 37652
GL_MAX_FRAMEBUFFER_WIDTH = 37653
GL_MAX_FRAMEBUFFER_HEIGHT = 37654
GL_MAX_FRAMEBUFFER_LAYERS = 37655
GL_MAX_FRAMEBUFFER_SAMPLES = 37656
GL_LOCATION_COMPONENT = 37706
GL_TRANSFORM_FEEDBACK_BUFFER_INDEX = 37707
GL_TRANSFORM_FEEDBACK_BUFFER_STRIDE = 37708
GL_CLIP_ORIGIN = 37724
GL_CLIP_DEPTH_MODE = 37725
GL_NEGATIVE_ONE_TO_ONE = 37726
GL_ZERO_TO_ONE = 37727
GL_CLEAR_TEXTURE = 37733
GL_NUM_SAMPLE_COUNTS = 37760
GL_MAX_MESH_TOTAL_MEMORY_SIZE_NV = 38198
GL_MAX_TASK_TOTAL_MEMORY_SIZE_NV = 38199
GL_MAX_MESH_OUTPUT_VERTICES_NV = 38200
GL_MAX_MESH_OUTPUT_PRIMITIVES_NV = 38201
GL_MAX_TASK_OUTPUT_COUNT_NV = 38202
GL_MAX_MESH_WORK_GROUP_SIZE_NV = 38203
GL_MAX_TASK_WORK_GROUP_SIZE_NV = 38204
GL_MAX_DRAW_MESH_TASKS_COUNT_NV = 38205
GL_MESH_WORK_GROUP_SIZE_NV = 38206
GL_TASK_WORK_GROUP_SIZE_NV = 38207
GL_MESH_OUTPUT_PER_PRIMITIVE_GRANULARITY_NV = 38211
GL_SHADER_BINARY_FORMAT_SPIR_V = 38225
GL_SPIR_V_BINARY = 38226
GL_SPIR_V_EXTENSIONS = 38227
GL_NUM_SPIR_V_EXTENSIONS = 38228
GL_MAX_MESH_VIEWS_NV = 38231
GL_MESH_SHADER_NV = 38233
GL_TASK_SHADER_NV = 38234
GL_MESH_VERTICES_OUT_NV = 38265
GL_MESH_PRIMITIVES_OUT_NV = 38266
GL_MESH_OUTPUT_TYPE_NV = 38267
GL_MESH_SUBROUTINE_NV = 38268
GL_TASK_SUBROUTINE_NV = 38269
GL_MESH_SUBROUTINE_UNIFORM_NV = 38270
GL_TASK_SUBROUTINE_UNIFORM_NV = 38271
GL_UNIFORM_BLOCK_REFERENCED_BY_MESH_SHADER_NV = 38300
GL_UNIFORM_BLOCK_REFERENCED_BY_TASK_SHADER_NV = 38301
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_MESH_SHADER_NV = 38302
GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TASK_SHADER_NV = 38303
GL_REFERENCED_BY_MESH_SHADER_NV = 38304
GL_REFERENCED_BY_TASK_SHADER_NV = 38305
GL_MAX_MESH_WORK_GROUP_INVOCATIONS_NV = 38306
GL_MAX_TASK_WORK_GROUP_INVOCATIONS_NV = 38307
GL_EVAL_BIT = 65536
GL_LIST_BIT = 131072
GL_TEXTURE_BIT = 262144
GL_SCISSOR_BIT = 524288
GL_MULTISAMPLE_BIT = 536870912
GL_MULTISAMPLE_BIT_ARB = 536870912
GL_ALL_ATTRIB_BITS = 4294967295
GL_CLIENT_ALL_ATTRIB_BITS = 4294967295
GL_INVALID_INDEX = 4294967295
GL_ALL_SHADER_BITS = 4294967295
GL_ALL_BARRIER_BITS = 4294967295
GL_TIMEOUT_IGNORED = 18446744073709551615

# GL command definitions
glAccum = _link_function('glAccum', None, [GLenum, GLfloat], requires='OpenGL 1.0')
glActiveShaderProgram = _link_function('glActiveShaderProgram', None, [GLuint, GLuint], requires='OpenGL 4.1')
glActiveTexture = _link_function('glActiveTexture', None, [GLenum], requires='OpenGL 1.3')
glAlphaFunc = _link_function('glAlphaFunc', None, [GLenum, GLfloat], requires='OpenGL 1.0')
glAreTexturesResident = _link_function('glAreTexturesResident', GLboolean, [GLsizei, POINTER(GLuint), POINTER(GLboolean)], requires='OpenGL 1.1')
glArrayElement = _link_function('glArrayElement', None, [GLint], requires='OpenGL 1.1')
glAttachShader = _link_function('glAttachShader', None, [GLuint, GLuint], requires='OpenGL 2.0')
glBegin = _link_function('glBegin', None, [GLenum], requires='OpenGL 1.0')
glBeginConditionalRender = _link_function('glBeginConditionalRender', None, [GLuint, GLenum], requires='OpenGL 3.0')
glBeginQuery = _link_function('glBeginQuery', None, [GLenum, GLuint], requires='OpenGL 1.5')
glBeginQueryIndexed = _link_function('glBeginQueryIndexed', None, [GLenum, GLuint, GLuint], requires='OpenGL 4.0')
glBeginTransformFeedback = _link_function('glBeginTransformFeedback', None, [GLenum], requires='OpenGL 3.0')
glBindAttribLocation = _link_function('glBindAttribLocation', None, [GLuint, GLuint, POINTER(GLchar)], requires='OpenGL 2.0')
glBindBuffer = _link_function('glBindBuffer', None, [GLenum, GLuint], requires='OpenGL 1.5')
glBindBufferBase = _link_function('glBindBufferBase', None, [GLenum, GLuint, GLuint], requires='OpenGL 3.1')
glBindBufferRange = _link_function('glBindBufferRange', None, [GLenum, GLuint, GLuint, GLintptr, GLsizeiptr], requires='OpenGL 3.1')
glBindBuffersBase = _link_function('glBindBuffersBase', None, [GLenum, GLuint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.4')
glBindBuffersRange = _link_function('glBindBuffersRange', None, [GLenum, GLuint, GLsizei, POINTER(GLuint), POINTER(GLintptr), POINTER(GLsizeiptr)], requires='OpenGL 4.4')
glBindFragDataLocation = _link_function('glBindFragDataLocation', None, [GLuint, GLuint, POINTER(GLchar)], requires='OpenGL 3.0')
glBindFragDataLocationIndexed = _link_function('glBindFragDataLocationIndexed', None, [GLuint, GLuint, GLuint, POINTER(GLchar)], requires='OpenGL 3.3')
glBindFramebuffer = _link_function('glBindFramebuffer', None, [GLenum, GLuint], requires='OpenGL 3.0')
glBindFramebufferEXT = _link_function('glBindFramebufferEXT', None, [GLenum, GLuint], requires='None')
glBindImageTexture = _link_function('glBindImageTexture', None, [GLuint, GLuint, GLint, GLboolean, GLint, GLenum, GLenum], requires='OpenGL 4.2')
glBindImageTextures = _link_function('glBindImageTextures', None, [GLuint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.4')
glBindProgramPipeline = _link_function('glBindProgramPipeline', None, [GLuint], requires='OpenGL 4.1')
glBindRenderbuffer = _link_function('glBindRenderbuffer', None, [GLenum, GLuint], requires='OpenGL 3.0')
glBindRenderbufferEXT = _link_function('glBindRenderbufferEXT', None, [GLenum, GLuint], requires='None')
glBindSampler = _link_function('glBindSampler', None, [GLuint, GLuint], requires='OpenGL 3.3')
glBindSamplers = _link_function('glBindSamplers', None, [GLuint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.4')
glBindTexture = _link_function('glBindTexture', None, [GLenum, GLuint], requires='OpenGL 1.1')
glBindTextureUnit = _link_function('glBindTextureUnit', None, [GLuint, GLuint], requires='OpenGL 4.5')
glBindTextures = _link_function('glBindTextures', None, [GLuint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.4')
glBindTransformFeedback = _link_function('glBindTransformFeedback', None, [GLenum, GLuint], requires='OpenGL 4.0')
glBindVertexArray = _link_function('glBindVertexArray', None, [GLuint], requires='OpenGL 3.0')
glBindVertexBuffer = _link_function('glBindVertexBuffer', None, [GLuint, GLuint, GLintptr, GLsizei], requires='OpenGL 4.3')
glBindVertexBuffers = _link_function('glBindVertexBuffers', None, [GLuint, GLsizei, POINTER(GLuint), POINTER(GLintptr), POINTER(GLsizei)], requires='OpenGL 4.4')
glBitmap = _link_function('glBitmap', None, [GLsizei, GLsizei, GLfloat, GLfloat, GLfloat, GLfloat, POINTER(GLubyte)], requires='OpenGL 1.0')
glBlendColor = _link_function('glBlendColor', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.4')
glBlendEquation = _link_function('glBlendEquation', None, [GLenum], requires='OpenGL 1.4')
glBlendEquationSeparate = _link_function('glBlendEquationSeparate', None, [GLenum, GLenum], requires='OpenGL 2.0')
glBlendEquationSeparatei = _link_function('glBlendEquationSeparatei', None, [GLuint, GLenum, GLenum], requires='OpenGL 4.0')
glBlendEquationi = _link_function('glBlendEquationi', None, [GLuint, GLenum], requires='OpenGL 4.0')
glBlendFunc = _link_function('glBlendFunc', None, [GLenum, GLenum], requires='OpenGL 1.0')
glBlendFuncSeparate = _link_function('glBlendFuncSeparate', None, [GLenum, GLenum, GLenum, GLenum], requires='OpenGL 1.4')
glBlendFuncSeparatei = _link_function('glBlendFuncSeparatei', None, [GLuint, GLenum, GLenum, GLenum, GLenum], requires='OpenGL 4.0')
glBlendFunci = _link_function('glBlendFunci', None, [GLuint, GLenum, GLenum], requires='OpenGL 4.0')
glBlitFramebuffer = _link_function('glBlitFramebuffer', None, [GLint, GLint, GLint, GLint, GLint, GLint, GLint, GLint, GLbitfield, GLenum], requires='OpenGL 3.0')
glBlitNamedFramebuffer = _link_function('glBlitNamedFramebuffer', None, [GLuint, GLuint, GLint, GLint, GLint, GLint, GLint, GLint, GLint, GLint, GLbitfield, GLenum], requires='OpenGL 4.5')
glBufferData = _link_function('glBufferData', None, [GLenum, GLsizeiptr, POINTER(GLvoid), GLenum], requires='OpenGL 1.5')
glBufferStorage = _link_function('glBufferStorage', None, [GLenum, GLsizeiptr, POINTER(GLvoid), GLbitfield], requires='OpenGL 4.4')
glBufferSubData = _link_function('glBufferSubData', None, [GLenum, GLintptr, GLsizeiptr, POINTER(GLvoid)], requires='OpenGL 1.5')
glCallList = _link_function('glCallList', None, [GLuint], requires='OpenGL 1.0')
glCallLists = _link_function('glCallLists', None, [GLsizei, GLenum, POINTER(GLvoid)], requires='OpenGL 1.0')
glCheckFramebufferStatus = _link_function('glCheckFramebufferStatus', GLenum, [GLenum], requires='OpenGL 3.0')
glCheckFramebufferStatusEXT = _link_function('glCheckFramebufferStatusEXT', GLenum, [GLenum], requires='None')
glCheckNamedFramebufferStatus = _link_function('glCheckNamedFramebufferStatus', GLenum, [GLuint, GLenum], requires='OpenGL 4.5')
glClampColor = _link_function('glClampColor', None, [GLenum, GLenum], requires='OpenGL 3.0')
glClear = _link_function('glClear', None, [GLbitfield], requires='OpenGL 1.0')
glClearAccum = _link_function('glClearAccum', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glClearBufferData = _link_function('glClearBufferData', None, [GLenum, GLenum, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.3')
glClearBufferSubData = _link_function('glClearBufferSubData', None, [GLenum, GLenum, GLintptr, GLsizeiptr, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.3')
glClearBufferfi = _link_function('glClearBufferfi', None, [GLenum, GLint, GLfloat, GLint], requires='OpenGL 3.0')
glClearBufferfv = _link_function('glClearBufferfv', None, [GLenum, GLint, POINTER(GLfloat)], requires='OpenGL 3.0')
glClearBufferiv = _link_function('glClearBufferiv', None, [GLenum, GLint, POINTER(GLint)], requires='OpenGL 3.0')
glClearBufferuiv = _link_function('glClearBufferuiv', None, [GLenum, GLint, POINTER(GLuint)], requires='OpenGL 3.0')
glClearColor = _link_function('glClearColor', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glClearDepth = _link_function('glClearDepth', None, [GLdouble], requires='OpenGL 1.0')
glClearDepthf = _link_function('glClearDepthf', None, [GLfloat], requires='OpenGL 4.1')
glClearIndex = _link_function('glClearIndex', None, [GLfloat], requires='OpenGL 1.0')
glClearNamedBufferData = _link_function('glClearNamedBufferData', None, [GLuint, GLenum, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.5')
glClearNamedBufferSubData = _link_function('glClearNamedBufferSubData', None, [GLuint, GLenum, GLintptr, GLsizeiptr, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.5')
glClearNamedFramebufferfi = _link_function('glClearNamedFramebufferfi', None, [GLuint, GLenum, GLint, GLfloat, GLint], requires='OpenGL 4.5')
glClearNamedFramebufferfv = _link_function('glClearNamedFramebufferfv', None, [GLuint, GLenum, GLint, POINTER(GLfloat)], requires='OpenGL 4.5')
glClearNamedFramebufferiv = _link_function('glClearNamedFramebufferiv', None, [GLuint, GLenum, GLint, POINTER(GLint)], requires='OpenGL 4.5')
glClearNamedFramebufferuiv = _link_function('glClearNamedFramebufferuiv', None, [GLuint, GLenum, GLint, POINTER(GLuint)], requires='OpenGL 4.5')
glClearStencil = _link_function('glClearStencil', None, [GLint], requires='OpenGL 1.0')
glClearTexImage = _link_function('glClearTexImage', None, [GLuint, GLint, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.4')
glClearTexSubImage = _link_function('glClearTexSubImage', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.4')
glClientActiveTexture = _link_function('glClientActiveTexture', None, [GLenum], requires='OpenGL 1.3')
glClientWaitSync = _link_function('glClientWaitSync', GLenum, [GLsync, GLbitfield, GLuint64], requires='OpenGL 3.2')
glClipControl = _link_function('glClipControl', None, [GLenum, GLenum], requires='OpenGL 4.5')
glClipPlane = _link_function('glClipPlane', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.0')
glColor3b = _link_function('glColor3b', None, [GLbyte, GLbyte, GLbyte], requires='OpenGL 1.0')
glColor3bv = _link_function('glColor3bv', None, [POINTER(GLbyte)], requires='OpenGL 1.0')
glColor3d = _link_function('glColor3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glColor3dv = _link_function('glColor3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glColor3f = _link_function('glColor3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glColor3fv = _link_function('glColor3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glColor3i = _link_function('glColor3i', None, [GLint, GLint, GLint], requires='OpenGL 1.0')
glColor3iv = _link_function('glColor3iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glColor3s = _link_function('glColor3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glColor3sv = _link_function('glColor3sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glColor3ub = _link_function('glColor3ub', None, [GLubyte, GLubyte, GLubyte], requires='OpenGL 1.0')
glColor3ubv = _link_function('glColor3ubv', None, [POINTER(GLubyte)], requires='OpenGL 1.0')
glColor3ui = _link_function('glColor3ui', None, [GLuint, GLuint, GLuint], requires='OpenGL 1.0')
glColor3uiv = _link_function('glColor3uiv', None, [POINTER(GLuint)], requires='OpenGL 1.0')
glColor3us = _link_function('glColor3us', None, [GLushort, GLushort, GLushort], requires='OpenGL 1.0')
glColor3usv = _link_function('glColor3usv', None, [POINTER(GLushort)], requires='OpenGL 1.0')
glColor4b = _link_function('glColor4b', None, [GLbyte, GLbyte, GLbyte, GLbyte], requires='OpenGL 1.0')
glColor4bv = _link_function('glColor4bv', None, [POINTER(GLbyte)], requires='OpenGL 1.0')
glColor4d = _link_function('glColor4d', None, [GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glColor4dv = _link_function('glColor4dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glColor4f = _link_function('glColor4f', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glColor4fv = _link_function('glColor4fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glColor4i = _link_function('glColor4i', None, [GLint, GLint, GLint, GLint], requires='OpenGL 1.0')
glColor4iv = _link_function('glColor4iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glColor4s = _link_function('glColor4s', None, [GLshort, GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glColor4sv = _link_function('glColor4sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glColor4ub = _link_function('glColor4ub', None, [GLubyte, GLubyte, GLubyte, GLubyte], requires='OpenGL 1.0')
glColor4ubv = _link_function('glColor4ubv', None, [POINTER(GLubyte)], requires='OpenGL 1.0')
glColor4ui = _link_function('glColor4ui', None, [GLuint, GLuint, GLuint, GLuint], requires='OpenGL 1.0')
glColor4uiv = _link_function('glColor4uiv', None, [POINTER(GLuint)], requires='OpenGL 1.0')
glColor4us = _link_function('glColor4us', None, [GLushort, GLushort, GLushort, GLushort], requires='OpenGL 1.0')
glColor4usv = _link_function('glColor4usv', None, [POINTER(GLushort)], requires='OpenGL 1.0')
glColorMask = _link_function('glColorMask', None, [GLboolean, GLboolean, GLboolean, GLboolean], requires='OpenGL 1.0')
glColorMaski = _link_function('glColorMaski', None, [GLuint, GLboolean, GLboolean, GLboolean, GLboolean], requires='OpenGL 3.0')
glColorMaterial = _link_function('glColorMaterial', None, [GLenum, GLenum], requires='OpenGL 1.0')
glColorP3ui = _link_function('glColorP3ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glColorP3uiv = _link_function('glColorP3uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glColorP4ui = _link_function('glColorP4ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glColorP4uiv = _link_function('glColorP4uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glColorPointer = _link_function('glColorPointer', None, [GLint, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glCompileShader = _link_function('glCompileShader', None, [GLuint], requires='OpenGL 2.0')
glCompressedTexImage1D = _link_function('glCompressedTexImage1D', None, [GLenum, GLint, GLenum, GLsizei, GLint, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.3')
glCompressedTexImage2D = _link_function('glCompressedTexImage2D', None, [GLenum, GLint, GLenum, GLsizei, GLsizei, GLint, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.3')
glCompressedTexImage3D = _link_function('glCompressedTexImage3D', None, [GLenum, GLint, GLenum, GLsizei, GLsizei, GLsizei, GLint, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.3')
glCompressedTexSubImage1D = _link_function('glCompressedTexSubImage1D', None, [GLenum, GLint, GLint, GLsizei, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.3')
glCompressedTexSubImage2D = _link_function('glCompressedTexSubImage2D', None, [GLenum, GLint, GLint, GLint, GLsizei, GLsizei, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.3')
glCompressedTexSubImage3D = _link_function('glCompressedTexSubImage3D', None, [GLenum, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.3')
glCompressedTextureSubImage1D = _link_function('glCompressedTextureSubImage1D', None, [GLuint, GLint, GLint, GLsizei, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glCompressedTextureSubImage2D = _link_function('glCompressedTextureSubImage2D', None, [GLuint, GLint, GLint, GLint, GLsizei, GLsizei, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glCompressedTextureSubImage3D = _link_function('glCompressedTextureSubImage3D', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glCopyBufferSubData = _link_function('glCopyBufferSubData', None, [GLenum, GLenum, GLintptr, GLintptr, GLsizeiptr], requires='OpenGL 3.1')
glCopyImageSubData = _link_function('glCopyImageSubData', None, [GLuint, GLenum, GLint, GLint, GLint, GLint, GLuint, GLenum, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei], requires='OpenGL 4.3')
glCopyNamedBufferSubData = _link_function('glCopyNamedBufferSubData', None, [GLuint, GLuint, GLintptr, GLintptr, GLsizeiptr], requires='OpenGL 4.5')
glCopyPixels = _link_function('glCopyPixels', None, [GLint, GLint, GLsizei, GLsizei, GLenum], requires='OpenGL 1.0')
glCopyTexImage1D = _link_function('glCopyTexImage1D', None, [GLenum, GLint, GLenum, GLint, GLint, GLsizei, GLint], requires='OpenGL 1.1')
glCopyTexImage2D = _link_function('glCopyTexImage2D', None, [GLenum, GLint, GLenum, GLint, GLint, GLsizei, GLsizei, GLint], requires='OpenGL 1.1')
glCopyTexSubImage1D = _link_function('glCopyTexSubImage1D', None, [GLenum, GLint, GLint, GLint, GLint, GLsizei], requires='OpenGL 1.1')
glCopyTexSubImage2D = _link_function('glCopyTexSubImage2D', None, [GLenum, GLint, GLint, GLint, GLint, GLint, GLsizei, GLsizei], requires='OpenGL 1.1')
glCopyTexSubImage3D = _link_function('glCopyTexSubImage3D', None, [GLenum, GLint, GLint, GLint, GLint, GLint, GLint, GLsizei, GLsizei], requires='OpenGL 1.2')
glCopyTextureSubImage1D = _link_function('glCopyTextureSubImage1D', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei], requires='OpenGL 4.5')
glCopyTextureSubImage2D = _link_function('glCopyTextureSubImage2D', None, [GLuint, GLint, GLint, GLint, GLint, GLint, GLsizei, GLsizei], requires='OpenGL 4.5')
glCopyTextureSubImage3D = _link_function('glCopyTextureSubImage3D', None, [GLuint, GLint, GLint, GLint, GLint, GLint, GLint, GLsizei, GLsizei], requires='OpenGL 4.5')
glCreateBuffers = _link_function('glCreateBuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateFramebuffers = _link_function('glCreateFramebuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateProgram = _link_function('glCreateProgram', GLuint, [], requires='OpenGL 2.0')
glCreateProgramPipelines = _link_function('glCreateProgramPipelines', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateQueries = _link_function('glCreateQueries', None, [GLenum, GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateRenderbuffers = _link_function('glCreateRenderbuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateSamplers = _link_function('glCreateSamplers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateShader = _link_function('glCreateShader', GLuint, [GLenum], requires='OpenGL 2.0')
glCreateShaderProgramv = _link_function('glCreateShaderProgramv', GLuint, [GLenum, GLsizei, POINTER(POINTER(GLchar))], requires='OpenGL 4.1')
glCreateTextures = _link_function('glCreateTextures', None, [GLenum, GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateTransformFeedbacks = _link_function('glCreateTransformFeedbacks', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCreateVertexArrays = _link_function('glCreateVertexArrays', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glCullFace = _link_function('glCullFace', None, [GLenum], requires='OpenGL 1.0')
glDebugMessageCallback = _link_function('glDebugMessageCallback', None, [GLDEBUGPROC, POINTER(GLvoid)], requires='OpenGL 4.3')
glDebugMessageControl = _link_function('glDebugMessageControl', None, [GLenum, GLenum, GLenum, GLsizei, POINTER(GLuint), GLboolean], requires='OpenGL 4.3')
glDebugMessageInsert = _link_function('glDebugMessageInsert', None, [GLenum, GLenum, GLuint, GLenum, GLsizei, POINTER(GLchar)], requires='OpenGL 4.3')
glDeleteBuffers = _link_function('glDeleteBuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.5')
glDeleteFramebuffers = _link_function('glDeleteFramebuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glDeleteFramebuffersEXT = _link_function('glDeleteFramebuffersEXT', None, [GLsizei, POINTER(GLuint)], requires='None')
glDeleteLists = _link_function('glDeleteLists', None, [GLuint, GLsizei], requires='OpenGL 1.0')
glDeleteProgram = _link_function('glDeleteProgram', None, [GLuint], requires='OpenGL 2.0')
glDeleteProgramPipelines = _link_function('glDeleteProgramPipelines', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.1')
glDeleteQueries = _link_function('glDeleteQueries', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.5')
glDeleteRenderbuffers = _link_function('glDeleteRenderbuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glDeleteRenderbuffersEXT = _link_function('glDeleteRenderbuffersEXT', None, [GLsizei, POINTER(GLuint)], requires='None')
glDeleteSamplers = _link_function('glDeleteSamplers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.3')
glDeleteShader = _link_function('glDeleteShader', None, [GLuint], requires='OpenGL 2.0')
glDeleteSync = _link_function('glDeleteSync', None, [GLsync], requires='OpenGL 3.2')
glDeleteTextures = _link_function('glDeleteTextures', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.1')
glDeleteTransformFeedbacks = _link_function('glDeleteTransformFeedbacks', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.0')
glDeleteVertexArrays = _link_function('glDeleteVertexArrays', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glDepthFunc = _link_function('glDepthFunc', None, [GLenum], requires='OpenGL 1.0')
glDepthMask = _link_function('glDepthMask', None, [GLboolean], requires='OpenGL 1.0')
glDepthRange = _link_function('glDepthRange', None, [GLdouble, GLdouble], requires='OpenGL 1.0')
glDepthRangeArrayv = _link_function('glDepthRangeArrayv', None, [GLuint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.1')
glDepthRangeIndexed = _link_function('glDepthRangeIndexed', None, [GLuint, GLdouble, GLdouble], requires='OpenGL 4.1')
glDepthRangef = _link_function('glDepthRangef', None, [GLfloat, GLfloat], requires='OpenGL 4.1')
glDetachShader = _link_function('glDetachShader', None, [GLuint, GLuint], requires='OpenGL 2.0')
glDisable = _link_function('glDisable', None, [GLenum], requires='OpenGL 1.0')
glDisableClientState = _link_function('glDisableClientState', None, [GLenum], requires='OpenGL 1.1')
glDisableVertexArrayAttrib = _link_function('glDisableVertexArrayAttrib', None, [GLuint, GLuint], requires='OpenGL 4.5')
glDisableVertexAttribArray = _link_function('glDisableVertexAttribArray', None, [GLuint], requires='OpenGL 2.0')
glDisablei = _link_function('glDisablei', None, [GLenum, GLuint], requires='OpenGL 3.0')
glDispatchCompute = _link_function('glDispatchCompute', None, [GLuint, GLuint, GLuint], requires='OpenGL 4.3')
glDispatchComputeIndirect = _link_function('glDispatchComputeIndirect', None, [GLintptr], requires='OpenGL 4.3')
glDrawArrays = _link_function('glDrawArrays', None, [GLenum, GLint, GLsizei], requires='OpenGL 1.1')
glDrawArraysIndirect = _link_function('glDrawArraysIndirect', None, [GLenum, POINTER(GLvoid)], requires='OpenGL 4.0')
glDrawArraysInstanced = _link_function('glDrawArraysInstanced', None, [GLenum, GLint, GLsizei, GLsizei], requires='OpenGL 3.1')
glDrawArraysInstancedBaseInstance = _link_function('glDrawArraysInstancedBaseInstance', None, [GLenum, GLint, GLsizei, GLsizei, GLuint], requires='OpenGL 4.2')
glDrawBuffer = _link_function('glDrawBuffer', None, [GLenum], requires='OpenGL 1.0')
glDrawBuffers = _link_function('glDrawBuffers', None, [GLsizei, POINTER(GLenum)], requires='OpenGL 2.0')
glDrawElements = _link_function('glDrawElements', None, [GLenum, GLsizei, GLenum, POINTER(GLvoid)], requires='OpenGL 1.1')
glDrawElementsBaseVertex = _link_function('glDrawElementsBaseVertex', None, [GLenum, GLsizei, GLenum, POINTER(GLvoid), GLint], requires='OpenGL 3.2')
glDrawElementsIndirect = _link_function('glDrawElementsIndirect', None, [GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.0')
glDrawElementsInstanced = _link_function('glDrawElementsInstanced', None, [GLenum, GLsizei, GLenum, POINTER(GLvoid), GLsizei], requires='OpenGL 3.1')
glDrawElementsInstancedBaseInstance = _link_function('glDrawElementsInstancedBaseInstance', None, [GLenum, GLsizei, GLenum, POINTER(GLvoid), GLsizei, GLuint], requires='OpenGL 4.2')
glDrawElementsInstancedBaseVertex = _link_function('glDrawElementsInstancedBaseVertex', None, [GLenum, GLsizei, GLenum, POINTER(GLvoid), GLsizei, GLint], requires='OpenGL 3.2')
glDrawElementsInstancedBaseVertexBaseInstance = _link_function('glDrawElementsInstancedBaseVertexBaseInstance', None, [GLenum, GLsizei, GLenum, POINTER(GLvoid), GLsizei, GLint, GLuint], requires='OpenGL 4.2')
glDrawMeshTasksIndirectNV = _link_function('glDrawMeshTasksIndirectNV', None, [GLintptr], requires='None')
glDrawMeshTasksNV = _link_function('glDrawMeshTasksNV', None, [GLuint, GLuint], requires='None')
glDrawPixels = _link_function('glDrawPixels', None, [GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.0')
glDrawRangeElements = _link_function('glDrawRangeElements', None, [GLenum, GLuint, GLuint, GLsizei, GLenum, POINTER(GLvoid)], requires='OpenGL 1.2')
glDrawRangeElementsBaseVertex = _link_function('glDrawRangeElementsBaseVertex', None, [GLenum, GLuint, GLuint, GLsizei, GLenum, POINTER(GLvoid), GLint], requires='OpenGL 3.2')
glDrawTransformFeedback = _link_function('glDrawTransformFeedback', None, [GLenum, GLuint], requires='OpenGL 4.0')
glDrawTransformFeedbackInstanced = _link_function('glDrawTransformFeedbackInstanced', None, [GLenum, GLuint, GLsizei], requires='OpenGL 4.2')
glDrawTransformFeedbackStream = _link_function('glDrawTransformFeedbackStream', None, [GLenum, GLuint, GLuint], requires='OpenGL 4.0')
glDrawTransformFeedbackStreamInstanced = _link_function('glDrawTransformFeedbackStreamInstanced', None, [GLenum, GLuint, GLuint, GLsizei], requires='OpenGL 4.2')
glEdgeFlag = _link_function('glEdgeFlag', None, [GLboolean], requires='OpenGL 1.0')
glEdgeFlagPointer = _link_function('glEdgeFlagPointer', None, [GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glEdgeFlagv = _link_function('glEdgeFlagv', None, [POINTER(GLboolean)], requires='OpenGL 1.0')
glEnable = _link_function('glEnable', None, [GLenum], requires='OpenGL 1.0')
glEnableClientState = _link_function('glEnableClientState', None, [GLenum], requires='OpenGL 1.1')
glEnableVertexArrayAttrib = _link_function('glEnableVertexArrayAttrib', None, [GLuint, GLuint], requires='OpenGL 4.5')
glEnableVertexAttribArray = _link_function('glEnableVertexAttribArray', None, [GLuint], requires='OpenGL 2.0')
glEnablei = _link_function('glEnablei', None, [GLenum, GLuint], requires='OpenGL 3.0')
glEnd = _link_function('glEnd', None, [], requires='OpenGL 1.0')
glEndConditionalRender = _link_function('glEndConditionalRender', None, [], requires='OpenGL 3.0')
glEndList = _link_function('glEndList', None, [], requires='OpenGL 1.0')
glEndQuery = _link_function('glEndQuery', None, [GLenum], requires='OpenGL 1.5')
glEndQueryIndexed = _link_function('glEndQueryIndexed', None, [GLenum, GLuint], requires='OpenGL 4.0')
glEndTransformFeedback = _link_function('glEndTransformFeedback', None, [], requires='OpenGL 3.0')
glEvalCoord1d = _link_function('glEvalCoord1d', None, [GLdouble], requires='OpenGL 1.0')
glEvalCoord1dv = _link_function('glEvalCoord1dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glEvalCoord1f = _link_function('glEvalCoord1f', None, [GLfloat], requires='OpenGL 1.0')
glEvalCoord1fv = _link_function('glEvalCoord1fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glEvalCoord2d = _link_function('glEvalCoord2d', None, [GLdouble, GLdouble], requires='OpenGL 1.0')
glEvalCoord2dv = _link_function('glEvalCoord2dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glEvalCoord2f = _link_function('glEvalCoord2f', None, [GLfloat, GLfloat], requires='OpenGL 1.0')
glEvalCoord2fv = _link_function('glEvalCoord2fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glEvalMesh1 = _link_function('glEvalMesh1', None, [GLenum, GLint, GLint], requires='OpenGL 1.0')
glEvalMesh2 = _link_function('glEvalMesh2', None, [GLenum, GLint, GLint, GLint, GLint], requires='OpenGL 1.0')
glEvalPoint1 = _link_function('glEvalPoint1', None, [GLint], requires='OpenGL 1.0')
glEvalPoint2 = _link_function('glEvalPoint2', None, [GLint, GLint], requires='OpenGL 1.0')
glFeedbackBuffer = _link_function('glFeedbackBuffer', None, [GLsizei, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glFenceSync = _link_function('glFenceSync', GLsync, [GLenum, GLbitfield], requires='OpenGL 3.2')
glFinish = _link_function('glFinish', None, [], requires='OpenGL 1.0')
glFlush = _link_function('glFlush', None, [], requires='OpenGL 1.0')
glFlushMappedBufferRange = _link_function('glFlushMappedBufferRange', None, [GLenum, GLintptr, GLsizeiptr], requires='OpenGL 3.0')
glFlushMappedNamedBufferRange = _link_function('glFlushMappedNamedBufferRange', None, [GLuint, GLintptr, GLsizeiptr], requires='OpenGL 4.5')
glFogCoordPointer = _link_function('glFogCoordPointer', None, [GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.4')
glFogCoordd = _link_function('glFogCoordd', None, [GLdouble], requires='OpenGL 1.4')
glFogCoorddv = _link_function('glFogCoorddv', None, [POINTER(GLdouble)], requires='OpenGL 1.4')
glFogCoordf = _link_function('glFogCoordf', None, [GLfloat], requires='OpenGL 1.4')
glFogCoordfv = _link_function('glFogCoordfv', None, [POINTER(GLfloat)], requires='OpenGL 1.4')
glFogf = _link_function('glFogf', None, [GLenum, GLfloat], requires='OpenGL 1.0')
glFogfv = _link_function('glFogfv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glFogi = _link_function('glFogi', None, [GLenum, GLint], requires='OpenGL 1.0')
glFogiv = _link_function('glFogiv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glFramebufferParameteri = _link_function('glFramebufferParameteri', None, [GLenum, GLenum, GLint], requires='OpenGL 4.3')
glFramebufferRenderbuffer = _link_function('glFramebufferRenderbuffer', None, [GLenum, GLenum, GLenum, GLuint], requires='OpenGL 3.0')
glFramebufferRenderbufferEXT = _link_function('glFramebufferRenderbufferEXT', None, [GLenum, GLenum, GLenum, GLuint], requires='None')
glFramebufferTexture = _link_function('glFramebufferTexture', None, [GLenum, GLenum, GLuint, GLint], requires='OpenGL 3.2')
glFramebufferTexture1D = _link_function('glFramebufferTexture1D', None, [GLenum, GLenum, GLenum, GLuint, GLint], requires='OpenGL 3.0')
glFramebufferTexture1DEXT = _link_function('glFramebufferTexture1DEXT', None, [GLenum, GLenum, GLenum, GLuint, GLint], requires='None')
glFramebufferTexture2D = _link_function('glFramebufferTexture2D', None, [GLenum, GLenum, GLenum, GLuint, GLint], requires='OpenGL 3.0')
glFramebufferTexture2DEXT = _link_function('glFramebufferTexture2DEXT', None, [GLenum, GLenum, GLenum, GLuint, GLint], requires='None')
glFramebufferTexture3D = _link_function('glFramebufferTexture3D', None, [GLenum, GLenum, GLenum, GLuint, GLint, GLint], requires='OpenGL 3.0')
glFramebufferTexture3DEXT = _link_function('glFramebufferTexture3DEXT', None, [GLenum, GLenum, GLenum, GLuint, GLint, GLint], requires='None')
glFramebufferTextureLayer = _link_function('glFramebufferTextureLayer', None, [GLenum, GLenum, GLuint, GLint, GLint], requires='OpenGL 3.0')
glFrontFace = _link_function('glFrontFace', None, [GLenum], requires='OpenGL 1.0')
glFrustum = _link_function('glFrustum', None, [GLdouble, GLdouble, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glGenBuffers = _link_function('glGenBuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.5')
glGenFramebuffers = _link_function('glGenFramebuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glGenFramebuffersEXT = _link_function('glGenFramebuffersEXT', None, [GLsizei, POINTER(GLuint)], requires='None')
glGenLists = _link_function('glGenLists', GLuint, [GLsizei], requires='OpenGL 1.0')
glGenProgramPipelines = _link_function('glGenProgramPipelines', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.1')
glGenQueries = _link_function('glGenQueries', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.5')
glGenRenderbuffers = _link_function('glGenRenderbuffers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glGenRenderbuffersEXT = _link_function('glGenRenderbuffersEXT', None, [GLsizei, POINTER(GLuint)], requires='None')
glGenSamplers = _link_function('glGenSamplers', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.3')
glGenTextures = _link_function('glGenTextures', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.1')
glGenTransformFeedbacks = _link_function('glGenTransformFeedbacks', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 4.0')
glGenVertexArrays = _link_function('glGenVertexArrays', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glGenerateMipmap = _link_function('glGenerateMipmap', None, [GLenum], requires='OpenGL 3.0')
glGenerateMipmapEXT = _link_function('glGenerateMipmapEXT', None, [GLenum], requires='None')
glGenerateTextureMipmap = _link_function('glGenerateTextureMipmap', None, [GLuint], requires='OpenGL 4.5')
glGetActiveAtomicCounterBufferiv = _link_function('glGetActiveAtomicCounterBufferiv', None, [GLuint, GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.2')
glGetActiveAttrib = _link_function('glGetActiveAttrib', None, [GLuint, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLint), POINTER(GLenum), POINTER(GLchar)], requires='OpenGL 2.0')
glGetActiveSubroutineName = _link_function('glGetActiveSubroutineName', None, [GLuint, GLenum, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.0')
glGetActiveSubroutineUniformName = _link_function('glGetActiveSubroutineUniformName', None, [GLuint, GLenum, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.0')
glGetActiveSubroutineUniformiv = _link_function('glGetActiveSubroutineUniformiv', None, [GLuint, GLenum, GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.0')
glGetActiveUniform = _link_function('glGetActiveUniform', None, [GLuint, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLint), POINTER(GLenum), POINTER(GLchar)], requires='OpenGL 2.0')
glGetActiveUniformBlockName = _link_function('glGetActiveUniformBlockName', None, [GLuint, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 3.1')
glGetActiveUniformBlockiv = _link_function('glGetActiveUniformBlockiv', None, [GLuint, GLuint, GLenum, POINTER(GLint)], requires='OpenGL 3.1')
glGetActiveUniformName = _link_function('glGetActiveUniformName', None, [GLuint, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 3.1')
glGetActiveUniformsiv = _link_function('glGetActiveUniformsiv', None, [GLuint, GLsizei, POINTER(GLuint), GLenum, POINTER(GLint)], requires='OpenGL 3.1')
glGetAttachedShaders = _link_function('glGetAttachedShaders', None, [GLuint, GLsizei, POINTER(GLsizei), POINTER(GLuint)], requires='OpenGL 2.0')
glGetAttribLocation = _link_function('glGetAttribLocation', GLint, [GLuint, POINTER(GLchar)], requires='OpenGL 2.0')
glGetBooleani_v = _link_function('glGetBooleani_v', None, [GLenum, GLuint, POINTER(GLboolean)], requires='OpenGL 3.0')
glGetBooleanv = _link_function('glGetBooleanv', None, [GLenum, POINTER(GLboolean)], requires='OpenGL 1.0')
glGetBufferParameteri64v = _link_function('glGetBufferParameteri64v', None, [GLenum, GLenum, POINTER(GLint64)], requires='OpenGL 3.2')
glGetBufferParameteriv = _link_function('glGetBufferParameteriv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.5')
glGetBufferPointerv = _link_function('glGetBufferPointerv', None, [GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.5')
glGetBufferSubData = _link_function('glGetBufferSubData', None, [GLenum, GLintptr, GLsizeiptr, POINTER(GLvoid)], requires='OpenGL 1.5')
glGetClipPlane = _link_function('glGetClipPlane', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.0')
glGetCompressedTexImage = _link_function('glGetCompressedTexImage', None, [GLenum, GLint, POINTER(GLvoid)], requires='OpenGL 1.3')
glGetCompressedTextureImage = _link_function('glGetCompressedTextureImage', None, [GLuint, GLint, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetCompressedTextureSubImage = _link_function('glGetCompressedTextureSubImage', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetDebugMessageLog = _link_function('glGetDebugMessageLog', GLuint, [GLuint, GLsizei, POINTER(GLenum), POINTER(GLenum), POINTER(GLuint), POINTER(GLenum), POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.3')
glGetDoublei_v = _link_function('glGetDoublei_v', None, [GLenum, GLuint, POINTER(GLdouble)], requires='OpenGL 4.1')
glGetDoublev = _link_function('glGetDoublev', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.0')
glGetError = _link_function('glGetError', GLenum, [], requires='OpenGL 1.0')
glGetFloati_v = _link_function('glGetFloati_v', None, [GLenum, GLuint, POINTER(GLfloat)], requires='OpenGL 4.1')
glGetFloatv = _link_function('glGetFloatv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetFragDataIndex = _link_function('glGetFragDataIndex', GLint, [GLuint, POINTER(GLchar)], requires='OpenGL 3.3')
glGetFragDataLocation = _link_function('glGetFragDataLocation', GLint, [GLuint, POINTER(GLchar)], requires='OpenGL 3.0')
glGetFramebufferAttachmentParameteriv = _link_function('glGetFramebufferAttachmentParameteriv', None, [GLenum, GLenum, GLenum, POINTER(GLint)], requires='OpenGL 3.0')
glGetFramebufferAttachmentParameterivEXT = _link_function('glGetFramebufferAttachmentParameterivEXT', None, [GLenum, GLenum, GLenum, POINTER(GLint)], requires='None')
glGetFramebufferParameteriv = _link_function('glGetFramebufferParameteriv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 4.3')
glGetGraphicsResetStatus = _link_function('glGetGraphicsResetStatus', GLenum, [], requires='OpenGL 4.5')
glGetImageHandleARB = _link_function('glGetImageHandleARB', GLuint64, [GLuint, GLint, GLboolean, GLint, GLenum], requires='None')
glGetInteger64i_v = _link_function('glGetInteger64i_v', None, [GLenum, GLuint, POINTER(GLint64)], requires='OpenGL 3.2')
glGetInteger64v = _link_function('glGetInteger64v', None, [GLenum, POINTER(GLint64)], requires='OpenGL 3.2')
glGetIntegeri_v = _link_function('glGetIntegeri_v', None, [GLenum, GLuint, POINTER(GLint)], requires='OpenGL 3.1')
glGetIntegerv = _link_function('glGetIntegerv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetInternalformati64v = _link_function('glGetInternalformati64v', None, [GLenum, GLenum, GLenum, GLsizei, POINTER(GLint64)], requires='OpenGL 4.3')
glGetInternalformativ = _link_function('glGetInternalformativ', None, [GLenum, GLenum, GLenum, GLsizei, POINTER(GLint)], requires='OpenGL 4.2')
glGetLightfv = _link_function('glGetLightfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetLightiv = _link_function('glGetLightiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetMapdv = _link_function('glGetMapdv', None, [GLenum, GLenum, POINTER(GLdouble)], requires='OpenGL 1.0')
glGetMapfv = _link_function('glGetMapfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetMapiv = _link_function('glGetMapiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetMaterialfv = _link_function('glGetMaterialfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetMaterialiv = _link_function('glGetMaterialiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetMultisamplefv = _link_function('glGetMultisamplefv', None, [GLenum, GLuint, POINTER(GLfloat)], requires='OpenGL 3.2')
glGetNamedBufferParameteri64v = _link_function('glGetNamedBufferParameteri64v', None, [GLuint, GLenum, POINTER(GLint64)], requires='OpenGL 4.5')
glGetNamedBufferParameteriv = _link_function('glGetNamedBufferParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetNamedBufferPointerv = _link_function('glGetNamedBufferPointerv', None, [GLuint, GLenum, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetNamedBufferSubData = _link_function('glGetNamedBufferSubData', None, [GLuint, GLintptr, GLsizeiptr, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetNamedFramebufferAttachmentParameteriv = _link_function('glGetNamedFramebufferAttachmentParameteriv', None, [GLuint, GLenum, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetNamedFramebufferParameteriv = _link_function('glGetNamedFramebufferParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetNamedRenderbufferParameteriv = _link_function('glGetNamedRenderbufferParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetObjectLabel = _link_function('glGetObjectLabel', None, [GLenum, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.3')
glGetObjectPtrLabel = _link_function('glGetObjectPtrLabel', None, [POINTER(GLvoid), GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.3')
glGetPixelMapfv = _link_function('glGetPixelMapfv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetPixelMapuiv = _link_function('glGetPixelMapuiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 1.0')
glGetPixelMapusv = _link_function('glGetPixelMapusv', None, [GLenum, POINTER(GLushort)], requires='OpenGL 1.0')
glGetPointerv = _link_function('glGetPointerv', None, [GLenum, POINTER(GLvoid)], requires='OpenGL 4.3')
glGetPolygonStipple = _link_function('glGetPolygonStipple', None, [POINTER(GLubyte)], requires='OpenGL 1.0')
glGetProgramBinary = _link_function('glGetProgramBinary', None, [GLuint, GLsizei, POINTER(GLsizei), POINTER(GLenum), POINTER(GLvoid)], requires='OpenGL 4.1')
glGetProgramInfoLog = _link_function('glGetProgramInfoLog', None, [GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 2.0')
glGetProgramInterfaceiv = _link_function('glGetProgramInterfaceiv', None, [GLuint, GLenum, GLenum, POINTER(GLint)], requires='OpenGL 4.3')
glGetProgramPipelineInfoLog = _link_function('glGetProgramPipelineInfoLog', None, [GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.1')
glGetProgramPipelineiv = _link_function('glGetProgramPipelineiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.1')
glGetProgramResourceIndex = _link_function('glGetProgramResourceIndex', GLuint, [GLuint, GLenum, POINTER(GLchar)], requires='OpenGL 4.3')
glGetProgramResourceLocation = _link_function('glGetProgramResourceLocation', GLint, [GLuint, GLenum, POINTER(GLchar)], requires='OpenGL 4.3')
glGetProgramResourceLocationIndex = _link_function('glGetProgramResourceLocationIndex', GLint, [GLuint, GLenum, POINTER(GLchar)], requires='OpenGL 4.3')
glGetProgramResourceName = _link_function('glGetProgramResourceName', None, [GLuint, GLenum, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 4.3')
glGetProgramResourceiv = _link_function('glGetProgramResourceiv', None, [GLuint, GLenum, GLuint, GLsizei, POINTER(GLenum), GLsizei, POINTER(GLsizei), POINTER(GLint)], requires='OpenGL 4.3')
glGetProgramStageiv = _link_function('glGetProgramStageiv', None, [GLuint, GLenum, GLenum, POINTER(GLint)], requires='OpenGL 4.0')
glGetProgramiv = _link_function('glGetProgramiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 2.0')
glGetQueryBufferObjecti64v = _link_function('glGetQueryBufferObjecti64v', None, [GLuint, GLuint, GLenum, GLintptr], requires='OpenGL 4.5')
glGetQueryBufferObjectiv = _link_function('glGetQueryBufferObjectiv', None, [GLuint, GLuint, GLenum, GLintptr], requires='OpenGL 4.5')
glGetQueryBufferObjectui64v = _link_function('glGetQueryBufferObjectui64v', None, [GLuint, GLuint, GLenum, GLintptr], requires='OpenGL 4.5')
glGetQueryBufferObjectuiv = _link_function('glGetQueryBufferObjectuiv', None, [GLuint, GLuint, GLenum, GLintptr], requires='OpenGL 4.5')
glGetQueryIndexediv = _link_function('glGetQueryIndexediv', None, [GLenum, GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.0')
glGetQueryObjecti64v = _link_function('glGetQueryObjecti64v', None, [GLuint, GLenum, POINTER(GLint64)], requires='OpenGL 3.3')
glGetQueryObjectiv = _link_function('glGetQueryObjectiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 1.5')
glGetQueryObjectui64v = _link_function('glGetQueryObjectui64v', None, [GLuint, GLenum, POINTER(GLuint64)], requires='OpenGL 3.3')
glGetQueryObjectuiv = _link_function('glGetQueryObjectuiv', None, [GLuint, GLenum, POINTER(GLuint)], requires='OpenGL 1.5')
glGetQueryiv = _link_function('glGetQueryiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.5')
glGetRenderbufferParameteriv = _link_function('glGetRenderbufferParameteriv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 3.0')
glGetRenderbufferParameterivEXT = _link_function('glGetRenderbufferParameterivEXT', None, [GLenum, GLenum, POINTER(GLint)], requires='None')
glGetSamplerParameterIiv = _link_function('glGetSamplerParameterIiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 3.3')
glGetSamplerParameterIuiv = _link_function('glGetSamplerParameterIuiv', None, [GLuint, GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glGetSamplerParameterfv = _link_function('glGetSamplerParameterfv', None, [GLuint, GLenum, POINTER(GLfloat)], requires='OpenGL 3.3')
glGetSamplerParameteriv = _link_function('glGetSamplerParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 3.3')
glGetShaderInfoLog = _link_function('glGetShaderInfoLog', None, [GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 2.0')
glGetShaderPrecisionFormat = _link_function('glGetShaderPrecisionFormat', None, [GLenum, GLenum, POINTER(GLint), POINTER(GLint)], requires='OpenGL 4.1')
glGetShaderSource = _link_function('glGetShaderSource', None, [GLuint, GLsizei, POINTER(GLsizei), POINTER(GLchar)], requires='OpenGL 2.0')
glGetShaderiv = _link_function('glGetShaderiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 2.0')
glGetString = _link_function('glGetString', POINTER(GLubyte), [GLenum], requires='OpenGL 1.0')
glGetStringi = _link_function('glGetStringi', POINTER(GLubyte), [GLenum, GLuint], requires='OpenGL 3.0')
glGetSubroutineIndex = _link_function('glGetSubroutineIndex', GLuint, [GLuint, GLenum, POINTER(GLchar)], requires='OpenGL 4.0')
glGetSubroutineUniformLocation = _link_function('glGetSubroutineUniformLocation', GLint, [GLuint, GLenum, POINTER(GLchar)], requires='OpenGL 4.0')
glGetSynciv = _link_function('glGetSynciv', None, [GLsync, GLenum, GLsizei, POINTER(GLsizei), POINTER(GLint)], requires='OpenGL 3.2')
glGetTexEnvfv = _link_function('glGetTexEnvfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetTexEnviv = _link_function('glGetTexEnviv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetTexGendv = _link_function('glGetTexGendv', None, [GLenum, GLenum, POINTER(GLdouble)], requires='OpenGL 1.0')
glGetTexGenfv = _link_function('glGetTexGenfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetTexGeniv = _link_function('glGetTexGeniv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetTexImage = _link_function('glGetTexImage', None, [GLenum, GLint, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.0')
glGetTexLevelParameterfv = _link_function('glGetTexLevelParameterfv', None, [GLenum, GLint, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetTexLevelParameteriv = _link_function('glGetTexLevelParameteriv', None, [GLenum, GLint, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetTexParameterIiv = _link_function('glGetTexParameterIiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 3.0')
glGetTexParameterIuiv = _link_function('glGetTexParameterIuiv', None, [GLenum, GLenum, POINTER(GLuint)], requires='OpenGL 3.0')
glGetTexParameterfv = _link_function('glGetTexParameterfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glGetTexParameteriv = _link_function('glGetTexParameteriv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glGetTextureHandleARB = _link_function('glGetTextureHandleARB', GLuint64, [GLuint], requires='None')
glGetTextureImage = _link_function('glGetTextureImage', None, [GLuint, GLint, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetTextureLevelParameterfv = _link_function('glGetTextureLevelParameterfv', None, [GLuint, GLint, GLenum, POINTER(GLfloat)], requires='OpenGL 4.5')
glGetTextureLevelParameteriv = _link_function('glGetTextureLevelParameteriv', None, [GLuint, GLint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetTextureParameterIiv = _link_function('glGetTextureParameterIiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetTextureParameterIuiv = _link_function('glGetTextureParameterIuiv', None, [GLuint, GLenum, POINTER(GLuint)], requires='OpenGL 4.5')
glGetTextureParameterfv = _link_function('glGetTextureParameterfv', None, [GLuint, GLenum, POINTER(GLfloat)], requires='OpenGL 4.5')
glGetTextureParameteriv = _link_function('glGetTextureParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetTextureSamplerHandleARB = _link_function('glGetTextureSamplerHandleARB', GLuint64, [GLuint, GLuint], requires='None')
glGetTextureSubImage = _link_function('glGetTextureSubImage', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetTransformFeedbackVarying = _link_function('glGetTransformFeedbackVarying', None, [GLuint, GLuint, GLsizei, POINTER(GLsizei), POINTER(GLsizei), POINTER(GLenum), POINTER(GLchar)], requires='OpenGL 3.0')
glGetTransformFeedbacki64_v = _link_function('glGetTransformFeedbacki64_v', None, [GLuint, GLenum, GLuint, POINTER(GLint64)], requires='OpenGL 4.5')
glGetTransformFeedbacki_v = _link_function('glGetTransformFeedbacki_v', None, [GLuint, GLenum, GLuint, POINTER(GLint)], requires='OpenGL 4.5')
glGetTransformFeedbackiv = _link_function('glGetTransformFeedbackiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetUniformBlockIndex = _link_function('glGetUniformBlockIndex', GLuint, [GLuint, POINTER(GLchar)], requires='OpenGL 3.1')
glGetUniformIndices = _link_function('glGetUniformIndices', None, [GLuint, GLsizei, POINTER(POINTER(GLchar)), POINTER(GLuint)], requires='OpenGL 3.1')
glGetUniformLocation = _link_function('glGetUniformLocation', GLint, [GLuint, POINTER(GLchar)], requires='OpenGL 2.0')
glGetUniformSubroutineuiv = _link_function('glGetUniformSubroutineuiv', None, [GLenum, GLint, POINTER(GLuint)], requires='OpenGL 4.0')
glGetUniformdv = _link_function('glGetUniformdv', None, [GLuint, GLint, POINTER(GLdouble)], requires='OpenGL 4.0')
glGetUniformfv = _link_function('glGetUniformfv', None, [GLuint, GLint, POINTER(GLfloat)], requires='OpenGL 2.0')
glGetUniformi64vARB = _link_function('glGetUniformi64vARB', None, [GLuint, GLint, POINTER(GLint64)], requires='None')
glGetUniformiv = _link_function('glGetUniformiv', None, [GLuint, GLint, POINTER(GLint)], requires='OpenGL 2.0')
glGetUniformui64vARB = _link_function('glGetUniformui64vARB', None, [GLuint, GLint, POINTER(GLuint64)], requires='None')
glGetUniformuiv = _link_function('glGetUniformuiv', None, [GLuint, GLint, POINTER(GLuint)], requires='OpenGL 3.0')
glGetVertexArrayIndexed64iv = _link_function('glGetVertexArrayIndexed64iv', None, [GLuint, GLuint, GLenum, POINTER(GLint64)], requires='OpenGL 4.5')
glGetVertexArrayIndexediv = _link_function('glGetVertexArrayIndexediv', None, [GLuint, GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetVertexArrayiv = _link_function('glGetVertexArrayiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glGetVertexAttribIiv = _link_function('glGetVertexAttribIiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 3.0')
glGetVertexAttribIuiv = _link_function('glGetVertexAttribIuiv', None, [GLuint, GLenum, POINTER(GLuint)], requires='OpenGL 3.0')
glGetVertexAttribLdv = _link_function('glGetVertexAttribLdv', None, [GLuint, GLenum, POINTER(GLdouble)], requires='OpenGL 4.1')
glGetVertexAttribLui64vARB = _link_function('glGetVertexAttribLui64vARB', None, [GLuint, GLenum, POINTER(GLuint64EXT)], requires='None')
glGetVertexAttribPointerv = _link_function('glGetVertexAttribPointerv', None, [GLuint, GLenum, POINTER(GLvoid)], requires='OpenGL 2.0')
glGetVertexAttribdv = _link_function('glGetVertexAttribdv', None, [GLuint, GLenum, POINTER(GLdouble)], requires='OpenGL 2.0')
glGetVertexAttribfv = _link_function('glGetVertexAttribfv', None, [GLuint, GLenum, POINTER(GLfloat)], requires='OpenGL 2.0')
glGetVertexAttribiv = _link_function('glGetVertexAttribiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 2.0')
glGetnColorTable = _link_function('glGetnColorTable', None, [GLenum, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnCompressedTexImage = _link_function('glGetnCompressedTexImage', None, [GLenum, GLint, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnConvolutionFilter = _link_function('glGetnConvolutionFilter', None, [GLenum, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnHistogram = _link_function('glGetnHistogram', None, [GLenum, GLboolean, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnMapdv = _link_function('glGetnMapdv', None, [GLenum, GLenum, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.5')
glGetnMapfv = _link_function('glGetnMapfv', None, [GLenum, GLenum, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.5')
glGetnMapiv = _link_function('glGetnMapiv', None, [GLenum, GLenum, GLsizei, POINTER(GLint)], requires='OpenGL 4.5')
glGetnMinmax = _link_function('glGetnMinmax', None, [GLenum, GLboolean, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnPixelMapfv = _link_function('glGetnPixelMapfv', None, [GLenum, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.5')
glGetnPixelMapuiv = _link_function('glGetnPixelMapuiv', None, [GLenum, GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glGetnPixelMapusv = _link_function('glGetnPixelMapusv', None, [GLenum, GLsizei, POINTER(GLushort)], requires='OpenGL 4.5')
glGetnPolygonStipple = _link_function('glGetnPolygonStipple', None, [GLsizei, POINTER(GLubyte)], requires='OpenGL 4.5')
glGetnSeparableFilter = _link_function('glGetnSeparableFilter', None, [GLenum, GLenum, GLenum, GLsizei, POINTER(GLvoid), GLsizei, POINTER(GLvoid), POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnTexImage = _link_function('glGetnTexImage', None, [GLenum, GLint, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glGetnUniformdv = _link_function('glGetnUniformdv', None, [GLuint, GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.5')
glGetnUniformfv = _link_function('glGetnUniformfv', None, [GLuint, GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.5')
glGetnUniformi64vARB = _link_function('glGetnUniformi64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLint64)], requires='None')
glGetnUniformiv = _link_function('glGetnUniformiv', None, [GLuint, GLint, GLsizei, POINTER(GLint)], requires='OpenGL 4.5')
glGetnUniformui64vARB = _link_function('glGetnUniformui64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLuint64)], requires='None')
glGetnUniformuiv = _link_function('glGetnUniformuiv', None, [GLuint, GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.5')
glHint = _link_function('glHint', None, [GLenum, GLenum], requires='OpenGL 1.0')
glIndexMask = _link_function('glIndexMask', None, [GLuint], requires='OpenGL 1.0')
glIndexPointer = _link_function('glIndexPointer', None, [GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glIndexd = _link_function('glIndexd', None, [GLdouble], requires='OpenGL 1.0')
glIndexdv = _link_function('glIndexdv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glIndexf = _link_function('glIndexf', None, [GLfloat], requires='OpenGL 1.0')
glIndexfv = _link_function('glIndexfv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glIndexi = _link_function('glIndexi', None, [GLint], requires='OpenGL 1.0')
glIndexiv = _link_function('glIndexiv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glIndexs = _link_function('glIndexs', None, [GLshort], requires='OpenGL 1.0')
glIndexsv = _link_function('glIndexsv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glIndexub = _link_function('glIndexub', None, [GLubyte], requires='OpenGL 1.1')
glIndexubv = _link_function('glIndexubv', None, [POINTER(GLubyte)], requires='OpenGL 1.1')
glInitNames = _link_function('glInitNames', None, [], requires='OpenGL 1.0')
glInterleavedArrays = _link_function('glInterleavedArrays', None, [GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glInvalidateBufferData = _link_function('glInvalidateBufferData', None, [GLuint], requires='OpenGL 4.3')
glInvalidateBufferSubData = _link_function('glInvalidateBufferSubData', None, [GLuint, GLintptr, GLsizeiptr], requires='OpenGL 4.3')
glInvalidateFramebuffer = _link_function('glInvalidateFramebuffer', None, [GLenum, GLsizei, POINTER(GLenum)], requires='OpenGL 4.3')
glInvalidateNamedFramebufferData = _link_function('glInvalidateNamedFramebufferData', None, [GLuint, GLsizei, POINTER(GLenum)], requires='OpenGL 4.5')
glInvalidateNamedFramebufferSubData = _link_function('glInvalidateNamedFramebufferSubData', None, [GLuint, GLsizei, POINTER(GLenum), GLint, GLint, GLsizei, GLsizei], requires='OpenGL 4.5')
glInvalidateSubFramebuffer = _link_function('glInvalidateSubFramebuffer', None, [GLenum, GLsizei, POINTER(GLenum), GLint, GLint, GLsizei, GLsizei], requires='OpenGL 4.3')
glInvalidateTexImage = _link_function('glInvalidateTexImage', None, [GLuint, GLint], requires='OpenGL 4.3')
glInvalidateTexSubImage = _link_function('glInvalidateTexSubImage', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei], requires='OpenGL 4.3')
glIsBuffer = _link_function('glIsBuffer', GLboolean, [GLuint], requires='OpenGL 1.5')
glIsEnabled = _link_function('glIsEnabled', GLboolean, [GLenum], requires='OpenGL 1.0')
glIsEnabledi = _link_function('glIsEnabledi', GLboolean, [GLenum, GLuint], requires='OpenGL 3.0')
glIsFramebuffer = _link_function('glIsFramebuffer', GLboolean, [GLuint], requires='OpenGL 3.0')
glIsFramebufferEXT = _link_function('glIsFramebufferEXT', GLboolean, [GLuint], requires='None')
glIsImageHandleResidentARB = _link_function('glIsImageHandleResidentARB', GLboolean, [GLuint64], requires='None')
glIsList = _link_function('glIsList', GLboolean, [GLuint], requires='OpenGL 1.0')
glIsProgram = _link_function('glIsProgram', GLboolean, [GLuint], requires='OpenGL 2.0')
glIsProgramPipeline = _link_function('glIsProgramPipeline', GLboolean, [GLuint], requires='OpenGL 4.1')
glIsQuery = _link_function('glIsQuery', GLboolean, [GLuint], requires='OpenGL 1.5')
glIsRenderbuffer = _link_function('glIsRenderbuffer', GLboolean, [GLuint], requires='OpenGL 3.0')
glIsRenderbufferEXT = _link_function('glIsRenderbufferEXT', GLboolean, [GLuint], requires='None')
glIsSampler = _link_function('glIsSampler', GLboolean, [GLuint], requires='OpenGL 3.3')
glIsShader = _link_function('glIsShader', GLboolean, [GLuint], requires='OpenGL 2.0')
glIsSync = _link_function('glIsSync', GLboolean, [GLsync], requires='OpenGL 3.2')
glIsTexture = _link_function('glIsTexture', GLboolean, [GLuint], requires='OpenGL 1.1')
glIsTextureHandleResidentARB = _link_function('glIsTextureHandleResidentARB', GLboolean, [GLuint64], requires='None')
glIsTransformFeedback = _link_function('glIsTransformFeedback', GLboolean, [GLuint], requires='OpenGL 4.0')
glIsVertexArray = _link_function('glIsVertexArray', GLboolean, [GLuint], requires='OpenGL 3.0')
glLightModelf = _link_function('glLightModelf', None, [GLenum, GLfloat], requires='OpenGL 1.0')
glLightModelfv = _link_function('glLightModelfv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glLightModeli = _link_function('glLightModeli', None, [GLenum, GLint], requires='OpenGL 1.0')
glLightModeliv = _link_function('glLightModeliv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glLightf = _link_function('glLightf', None, [GLenum, GLenum, GLfloat], requires='OpenGL 1.0')
glLightfv = _link_function('glLightfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glLighti = _link_function('glLighti', None, [GLenum, GLenum, GLint], requires='OpenGL 1.0')
glLightiv = _link_function('glLightiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glLineStipple = _link_function('glLineStipple', None, [GLint, GLushort], requires='OpenGL 1.0')
glLineWidth = _link_function('glLineWidth', None, [GLfloat], requires='OpenGL 1.0')
glLinkProgram = _link_function('glLinkProgram', None, [GLuint], requires='OpenGL 2.0')
glListBase = _link_function('glListBase', None, [GLuint], requires='OpenGL 1.0')
glLoadIdentity = _link_function('glLoadIdentity', None, [], requires='OpenGL 1.0')
glLoadMatrixd = _link_function('glLoadMatrixd', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glLoadMatrixf = _link_function('glLoadMatrixf', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glLoadName = _link_function('glLoadName', None, [GLuint], requires='OpenGL 1.0')
glLoadTransposeMatrixd = _link_function('glLoadTransposeMatrixd', None, [POINTER(GLdouble)], requires='OpenGL 1.3')
glLoadTransposeMatrixf = _link_function('glLoadTransposeMatrixf', None, [POINTER(GLfloat)], requires='OpenGL 1.3')
glLogicOp = _link_function('glLogicOp', None, [GLenum], requires='OpenGL 1.0')
glMakeImageHandleNonResidentARB = _link_function('glMakeImageHandleNonResidentARB', None, [GLuint64], requires='None')
glMakeImageHandleResidentARB = _link_function('glMakeImageHandleResidentARB', None, [GLuint64, GLenum], requires='None')
glMakeTextureHandleNonResidentARB = _link_function('glMakeTextureHandleNonResidentARB', None, [GLuint64], requires='None')
glMakeTextureHandleResidentARB = _link_function('glMakeTextureHandleResidentARB', None, [GLuint64], requires='None')
glMap1d = _link_function('glMap1d', None, [GLenum, GLdouble, GLdouble, GLint, GLint, POINTER(GLdouble)], requires='OpenGL 1.0')
glMap1f = _link_function('glMap1f', None, [GLenum, GLfloat, GLfloat, GLint, GLint, POINTER(GLfloat)], requires='OpenGL 1.0')
glMap2d = _link_function('glMap2d', None, [GLenum, GLdouble, GLdouble, GLint, GLint, GLdouble, GLdouble, GLint, GLint, POINTER(GLdouble)], requires='OpenGL 1.0')
glMap2f = _link_function('glMap2f', None, [GLenum, GLfloat, GLfloat, GLint, GLint, GLfloat, GLfloat, GLint, GLint, POINTER(GLfloat)], requires='OpenGL 1.0')
glMapBuffer = _link_function('glMapBuffer', POINTER(None), [GLenum, GLenum], requires='OpenGL 1.5')
glMapBufferRange = _link_function('glMapBufferRange', POINTER(None), [GLenum, GLintptr, GLsizeiptr, GLbitfield], requires='OpenGL 3.0')
glMapGrid1d = _link_function('glMapGrid1d', None, [GLint, GLdouble, GLdouble], requires='OpenGL 1.0')
glMapGrid1f = _link_function('glMapGrid1f', None, [GLint, GLfloat, GLfloat], requires='OpenGL 1.0')
glMapGrid2d = _link_function('glMapGrid2d', None, [GLint, GLdouble, GLdouble, GLint, GLdouble, GLdouble], requires='OpenGL 1.0')
glMapGrid2f = _link_function('glMapGrid2f', None, [GLint, GLfloat, GLfloat, GLint, GLfloat, GLfloat], requires='OpenGL 1.0')
glMapNamedBuffer = _link_function('glMapNamedBuffer', POINTER(None), [GLuint, GLenum], requires='OpenGL 4.5')
glMapNamedBufferRange = _link_function('glMapNamedBufferRange', POINTER(None), [GLuint, GLintptr, GLsizeiptr, GLbitfield], requires='OpenGL 4.5')
glMaterialf = _link_function('glMaterialf', None, [GLenum, GLenum, GLfloat], requires='OpenGL 1.0')
glMaterialfv = _link_function('glMaterialfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glMateriali = _link_function('glMateriali', None, [GLenum, GLenum, GLint], requires='OpenGL 1.0')
glMaterialiv = _link_function('glMaterialiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glMatrixMode = _link_function('glMatrixMode', None, [GLenum], requires='OpenGL 1.0')
glMemoryBarrier = _link_function('glMemoryBarrier', None, [GLbitfield], requires='OpenGL 4.2')
glMemoryBarrierByRegion = _link_function('glMemoryBarrierByRegion', None, [GLbitfield], requires='OpenGL 4.5')
glMinSampleShading = _link_function('glMinSampleShading', None, [GLfloat], requires='OpenGL 4.0')
glMultMatrixd = _link_function('glMultMatrixd', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glMultMatrixf = _link_function('glMultMatrixf', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glMultTransposeMatrixd = _link_function('glMultTransposeMatrixd', None, [POINTER(GLdouble)], requires='OpenGL 1.3')
glMultTransposeMatrixf = _link_function('glMultTransposeMatrixf', None, [POINTER(GLfloat)], requires='OpenGL 1.3')
glMultiDrawArrays = _link_function('glMultiDrawArrays', None, [GLenum, POINTER(GLint), POINTER(GLsizei), GLsizei], requires='OpenGL 1.4')
glMultiDrawArraysIndirect = _link_function('glMultiDrawArraysIndirect', None, [GLenum, POINTER(GLvoid), GLsizei, GLsizei], requires='OpenGL 4.3')
glMultiDrawArraysIndirectCount = _link_function('glMultiDrawArraysIndirectCount', None, [GLenum, POINTER(GLvoid), GLintptr, GLsizei, GLsizei], requires='OpenGL 4.6')
glMultiDrawElements = _link_function('glMultiDrawElements', None, [GLenum, POINTER(GLsizei), GLenum, POINTER(GLvoid), GLsizei], requires='OpenGL 1.4')
glMultiDrawElementsBaseVertex = _link_function('glMultiDrawElementsBaseVertex', None, [GLenum, POINTER(GLsizei), GLenum, POINTER(GLvoid), GLsizei, POINTER(GLint)], requires='OpenGL 3.2')
glMultiDrawElementsIndirect = _link_function('glMultiDrawElementsIndirect', None, [GLenum, GLenum, POINTER(GLvoid), GLsizei, GLsizei], requires='OpenGL 4.3')
glMultiDrawElementsIndirectCount = _link_function('glMultiDrawElementsIndirectCount', None, [GLenum, GLenum, POINTER(GLvoid), GLintptr, GLsizei, GLsizei], requires='OpenGL 4.6')
glMultiDrawMeshTasksIndirectCountNV = _link_function('glMultiDrawMeshTasksIndirectCountNV', None, [GLintptr, GLintptr, GLsizei, GLsizei], requires='None')
glMultiDrawMeshTasksIndirectNV = _link_function('glMultiDrawMeshTasksIndirectNV', None, [GLintptr, GLsizei, GLsizei], requires='None')
glMultiTexCoord1d = _link_function('glMultiTexCoord1d', None, [GLenum, GLdouble], requires='OpenGL 1.3')
glMultiTexCoord1dv = _link_function('glMultiTexCoord1dv', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.3')
glMultiTexCoord1f = _link_function('glMultiTexCoord1f', None, [GLenum, GLfloat], requires='OpenGL 1.3')
glMultiTexCoord1fv = _link_function('glMultiTexCoord1fv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.3')
glMultiTexCoord1i = _link_function('glMultiTexCoord1i', None, [GLenum, GLint], requires='OpenGL 1.3')
glMultiTexCoord1iv = _link_function('glMultiTexCoord1iv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.3')
glMultiTexCoord1s = _link_function('glMultiTexCoord1s', None, [GLenum, GLshort], requires='OpenGL 1.3')
glMultiTexCoord1sv = _link_function('glMultiTexCoord1sv', None, [GLenum, POINTER(GLshort)], requires='OpenGL 1.3')
glMultiTexCoord2d = _link_function('glMultiTexCoord2d', None, [GLenum, GLdouble, GLdouble], requires='OpenGL 1.3')
glMultiTexCoord2dv = _link_function('glMultiTexCoord2dv', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.3')
glMultiTexCoord2f = _link_function('glMultiTexCoord2f', None, [GLenum, GLfloat, GLfloat], requires='OpenGL 1.3')
glMultiTexCoord2fv = _link_function('glMultiTexCoord2fv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.3')
glMultiTexCoord2i = _link_function('glMultiTexCoord2i', None, [GLenum, GLint, GLint], requires='OpenGL 1.3')
glMultiTexCoord2iv = _link_function('glMultiTexCoord2iv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.3')
glMultiTexCoord2s = _link_function('glMultiTexCoord2s', None, [GLenum, GLshort, GLshort], requires='OpenGL 1.3')
glMultiTexCoord2sv = _link_function('glMultiTexCoord2sv', None, [GLenum, POINTER(GLshort)], requires='OpenGL 1.3')
glMultiTexCoord3d = _link_function('glMultiTexCoord3d', None, [GLenum, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.3')
glMultiTexCoord3dv = _link_function('glMultiTexCoord3dv', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.3')
glMultiTexCoord3f = _link_function('glMultiTexCoord3f', None, [GLenum, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.3')
glMultiTexCoord3fv = _link_function('glMultiTexCoord3fv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.3')
glMultiTexCoord3i = _link_function('glMultiTexCoord3i', None, [GLenum, GLint, GLint, GLint], requires='OpenGL 1.3')
glMultiTexCoord3iv = _link_function('glMultiTexCoord3iv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.3')
glMultiTexCoord3s = _link_function('glMultiTexCoord3s', None, [GLenum, GLshort, GLshort, GLshort], requires='OpenGL 1.3')
glMultiTexCoord3sv = _link_function('glMultiTexCoord3sv', None, [GLenum, POINTER(GLshort)], requires='OpenGL 1.3')
glMultiTexCoord4d = _link_function('glMultiTexCoord4d', None, [GLenum, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.3')
glMultiTexCoord4dv = _link_function('glMultiTexCoord4dv', None, [GLenum, POINTER(GLdouble)], requires='OpenGL 1.3')
glMultiTexCoord4f = _link_function('glMultiTexCoord4f', None, [GLenum, GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.3')
glMultiTexCoord4fv = _link_function('glMultiTexCoord4fv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.3')
glMultiTexCoord4i = _link_function('glMultiTexCoord4i', None, [GLenum, GLint, GLint, GLint, GLint], requires='OpenGL 1.3')
glMultiTexCoord4iv = _link_function('glMultiTexCoord4iv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.3')
glMultiTexCoord4s = _link_function('glMultiTexCoord4s', None, [GLenum, GLshort, GLshort, GLshort, GLshort], requires='OpenGL 1.3')
glMultiTexCoord4sv = _link_function('glMultiTexCoord4sv', None, [GLenum, POINTER(GLshort)], requires='OpenGL 1.3')
glMultiTexCoordP1ui = _link_function('glMultiTexCoordP1ui', None, [GLenum, GLenum, GLuint], requires='OpenGL 3.3')
glMultiTexCoordP1uiv = _link_function('glMultiTexCoordP1uiv', None, [GLenum, GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glMultiTexCoordP2ui = _link_function('glMultiTexCoordP2ui', None, [GLenum, GLenum, GLuint], requires='OpenGL 3.3')
glMultiTexCoordP2uiv = _link_function('glMultiTexCoordP2uiv', None, [GLenum, GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glMultiTexCoordP3ui = _link_function('glMultiTexCoordP3ui', None, [GLenum, GLenum, GLuint], requires='OpenGL 3.3')
glMultiTexCoordP3uiv = _link_function('glMultiTexCoordP3uiv', None, [GLenum, GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glMultiTexCoordP4ui = _link_function('glMultiTexCoordP4ui', None, [GLenum, GLenum, GLuint], requires='OpenGL 3.3')
glMultiTexCoordP4uiv = _link_function('glMultiTexCoordP4uiv', None, [GLenum, GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glNamedBufferData = _link_function('glNamedBufferData', None, [GLuint, GLsizeiptr, POINTER(GLvoid), GLenum], requires='OpenGL 4.5')
glNamedBufferStorage = _link_function('glNamedBufferStorage', None, [GLuint, GLsizeiptr, POINTER(GLvoid), GLbitfield], requires='OpenGL 4.5')
glNamedBufferSubData = _link_function('glNamedBufferSubData', None, [GLuint, GLintptr, GLsizeiptr, POINTER(GLvoid)], requires='OpenGL 4.5')
glNamedFramebufferDrawBuffer = _link_function('glNamedFramebufferDrawBuffer', None, [GLuint, GLenum], requires='OpenGL 4.5')
glNamedFramebufferDrawBuffers = _link_function('glNamedFramebufferDrawBuffers', None, [GLuint, GLsizei, POINTER(GLenum)], requires='OpenGL 4.5')
glNamedFramebufferParameteri = _link_function('glNamedFramebufferParameteri', None, [GLuint, GLenum, GLint], requires='OpenGL 4.5')
glNamedFramebufferReadBuffer = _link_function('glNamedFramebufferReadBuffer', None, [GLuint, GLenum], requires='OpenGL 4.5')
glNamedFramebufferRenderbuffer = _link_function('glNamedFramebufferRenderbuffer', None, [GLuint, GLenum, GLenum, GLuint], requires='OpenGL 4.5')
glNamedFramebufferTexture = _link_function('glNamedFramebufferTexture', None, [GLuint, GLenum, GLuint, GLint], requires='OpenGL 4.5')
glNamedFramebufferTextureLayer = _link_function('glNamedFramebufferTextureLayer', None, [GLuint, GLenum, GLuint, GLint, GLint], requires='OpenGL 4.5')
glNamedRenderbufferStorage = _link_function('glNamedRenderbufferStorage', None, [GLuint, GLenum, GLsizei, GLsizei], requires='OpenGL 4.5')
glNamedRenderbufferStorageMultisample = _link_function('glNamedRenderbufferStorageMultisample', None, [GLuint, GLsizei, GLenum, GLsizei, GLsizei], requires='OpenGL 4.5')
glNewList = _link_function('glNewList', None, [GLuint, GLenum], requires='OpenGL 1.0')
glNormal3b = _link_function('glNormal3b', None, [GLbyte, GLbyte, GLbyte], requires='OpenGL 1.0')
glNormal3bv = _link_function('glNormal3bv', None, [POINTER(GLbyte)], requires='OpenGL 1.0')
glNormal3d = _link_function('glNormal3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glNormal3dv = _link_function('glNormal3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glNormal3f = _link_function('glNormal3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glNormal3fv = _link_function('glNormal3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glNormal3i = _link_function('glNormal3i', None, [GLint, GLint, GLint], requires='OpenGL 1.0')
glNormal3iv = _link_function('glNormal3iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glNormal3s = _link_function('glNormal3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glNormal3sv = _link_function('glNormal3sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glNormalP3ui = _link_function('glNormalP3ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glNormalP3uiv = _link_function('glNormalP3uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glNormalPointer = _link_function('glNormalPointer', None, [GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glObjectLabel = _link_function('glObjectLabel', None, [GLenum, GLuint, GLsizei, POINTER(GLchar)], requires='OpenGL 4.3')
glObjectPtrLabel = _link_function('glObjectPtrLabel', None, [POINTER(GLvoid), GLsizei, POINTER(GLchar)], requires='OpenGL 4.3')
glOrtho = _link_function('glOrtho', None, [GLdouble, GLdouble, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glPassThrough = _link_function('glPassThrough', None, [GLfloat], requires='OpenGL 1.0')
glPatchParameterfv = _link_function('glPatchParameterfv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 4.0')
glPatchParameteri = _link_function('glPatchParameteri', None, [GLenum, GLint], requires='OpenGL 4.0')
glPauseTransformFeedback = _link_function('glPauseTransformFeedback', None, [], requires='OpenGL 4.0')
glPixelMapfv = _link_function('glPixelMapfv', None, [GLenum, GLsizei, POINTER(GLfloat)], requires='OpenGL 1.0')
glPixelMapuiv = _link_function('glPixelMapuiv', None, [GLenum, GLsizei, POINTER(GLuint)], requires='OpenGL 1.0')
glPixelMapusv = _link_function('glPixelMapusv', None, [GLenum, GLsizei, POINTER(GLushort)], requires='OpenGL 1.0')
glPixelStoref = _link_function('glPixelStoref', None, [GLenum, GLfloat], requires='OpenGL 1.0')
glPixelStorei = _link_function('glPixelStorei', None, [GLenum, GLint], requires='OpenGL 1.0')
glPixelTransferf = _link_function('glPixelTransferf', None, [GLenum, GLfloat], requires='OpenGL 1.0')
glPixelTransferi = _link_function('glPixelTransferi', None, [GLenum, GLint], requires='OpenGL 1.0')
glPixelZoom = _link_function('glPixelZoom', None, [GLfloat, GLfloat], requires='OpenGL 1.0')
glPointParameterf = _link_function('glPointParameterf', None, [GLenum, GLfloat], requires='OpenGL 1.4')
glPointParameterfv = _link_function('glPointParameterfv', None, [GLenum, POINTER(GLfloat)], requires='OpenGL 1.4')
glPointParameteri = _link_function('glPointParameteri', None, [GLenum, GLint], requires='OpenGL 1.4')
glPointParameteriv = _link_function('glPointParameteriv', None, [GLenum, POINTER(GLint)], requires='OpenGL 1.4')
glPointSize = _link_function('glPointSize', None, [GLfloat], requires='OpenGL 1.0')
glPolygonMode = _link_function('glPolygonMode', None, [GLenum, GLenum], requires='OpenGL 1.0')
glPolygonOffset = _link_function('glPolygonOffset', None, [GLfloat, GLfloat], requires='OpenGL 1.1')
glPolygonOffsetClamp = _link_function('glPolygonOffsetClamp', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 4.6')
glPolygonStipple = _link_function('glPolygonStipple', None, [POINTER(GLubyte)], requires='OpenGL 1.0')
glPopAttrib = _link_function('glPopAttrib', None, [], requires='OpenGL 1.0')
glPopClientAttrib = _link_function('glPopClientAttrib', None, [], requires='OpenGL 1.1')
glPopDebugGroup = _link_function('glPopDebugGroup', None, [], requires='OpenGL 4.3')
glPopMatrix = _link_function('glPopMatrix', None, [], requires='OpenGL 1.0')
glPopName = _link_function('glPopName', None, [], requires='OpenGL 1.0')
glPrimitiveRestartIndex = _link_function('glPrimitiveRestartIndex', None, [GLuint], requires='OpenGL 3.1')
glPrioritizeTextures = _link_function('glPrioritizeTextures', None, [GLsizei, POINTER(GLuint), POINTER(GLfloat)], requires='OpenGL 1.1')
glProgramBinary = _link_function('glProgramBinary', None, [GLuint, GLenum, POINTER(GLvoid), GLsizei], requires='OpenGL 4.1')
glProgramParameteri = _link_function('glProgramParameteri', None, [GLuint, GLenum, GLint], requires='OpenGL 4.1')
glProgramUniform1d = _link_function('glProgramUniform1d', None, [GLuint, GLint, GLdouble], requires='OpenGL 4.1')
glProgramUniform1dv = _link_function('glProgramUniform1dv', None, [GLuint, GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniform1f = _link_function('glProgramUniform1f', None, [GLuint, GLint, GLfloat], requires='OpenGL 4.1')
glProgramUniform1fv = _link_function('glProgramUniform1fv', None, [GLuint, GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniform1i = _link_function('glProgramUniform1i', None, [GLuint, GLint, GLint], requires='OpenGL 4.1')
glProgramUniform1i64ARB = _link_function('glProgramUniform1i64ARB', None, [GLuint, GLint, GLint64], requires='None')
glProgramUniform1i64vARB = _link_function('glProgramUniform1i64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLint64)], requires='None')
glProgramUniform1iv = _link_function('glProgramUniform1iv', None, [GLuint, GLint, GLsizei, POINTER(GLint)], requires='OpenGL 4.1')
glProgramUniform1ui = _link_function('glProgramUniform1ui', None, [GLuint, GLint, GLuint], requires='OpenGL 4.1')
glProgramUniform1ui64ARB = _link_function('glProgramUniform1ui64ARB', None, [GLuint, GLint, GLuint64], requires='None')
glProgramUniform1ui64vARB = _link_function('glProgramUniform1ui64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLuint64)], requires='None')
glProgramUniform1uiv = _link_function('glProgramUniform1uiv', None, [GLuint, GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.1')
glProgramUniform2d = _link_function('glProgramUniform2d', None, [GLuint, GLint, GLdouble, GLdouble], requires='OpenGL 4.1')
glProgramUniform2dv = _link_function('glProgramUniform2dv', None, [GLuint, GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniform2f = _link_function('glProgramUniform2f', None, [GLuint, GLint, GLfloat, GLfloat], requires='OpenGL 4.1')
glProgramUniform2fv = _link_function('glProgramUniform2fv', None, [GLuint, GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniform2i = _link_function('glProgramUniform2i', None, [GLuint, GLint, GLint, GLint], requires='OpenGL 4.1')
glProgramUniform2i64ARB = _link_function('glProgramUniform2i64ARB', None, [GLuint, GLint, GLint64, GLint64], requires='None')
glProgramUniform2i64vARB = _link_function('glProgramUniform2i64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLint64)], requires='None')
glProgramUniform2iv = _link_function('glProgramUniform2iv', None, [GLuint, GLint, GLsizei, POINTER(GLint)], requires='OpenGL 4.1')
glProgramUniform2ui = _link_function('glProgramUniform2ui', None, [GLuint, GLint, GLuint, GLuint], requires='OpenGL 4.1')
glProgramUniform2ui64ARB = _link_function('glProgramUniform2ui64ARB', None, [GLuint, GLint, GLuint64, GLuint64], requires='None')
glProgramUniform2ui64vARB = _link_function('glProgramUniform2ui64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLuint64)], requires='None')
glProgramUniform2uiv = _link_function('glProgramUniform2uiv', None, [GLuint, GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.1')
glProgramUniform3d = _link_function('glProgramUniform3d', None, [GLuint, GLint, GLdouble, GLdouble, GLdouble], requires='OpenGL 4.1')
glProgramUniform3dv = _link_function('glProgramUniform3dv', None, [GLuint, GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniform3f = _link_function('glProgramUniform3f', None, [GLuint, GLint, GLfloat, GLfloat, GLfloat], requires='OpenGL 4.1')
glProgramUniform3fv = _link_function('glProgramUniform3fv', None, [GLuint, GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniform3i = _link_function('glProgramUniform3i', None, [GLuint, GLint, GLint, GLint, GLint], requires='OpenGL 4.1')
glProgramUniform3i64ARB = _link_function('glProgramUniform3i64ARB', None, [GLuint, GLint, GLint64, GLint64, GLint64], requires='None')
glProgramUniform3i64vARB = _link_function('glProgramUniform3i64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLint64)], requires='None')
glProgramUniform3iv = _link_function('glProgramUniform3iv', None, [GLuint, GLint, GLsizei, POINTER(GLint)], requires='OpenGL 4.1')
glProgramUniform3ui = _link_function('glProgramUniform3ui', None, [GLuint, GLint, GLuint, GLuint, GLuint], requires='OpenGL 4.1')
glProgramUniform3ui64ARB = _link_function('glProgramUniform3ui64ARB', None, [GLuint, GLint, GLuint64, GLuint64, GLuint64], requires='None')
glProgramUniform3ui64vARB = _link_function('glProgramUniform3ui64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLuint64)], requires='None')
glProgramUniform3uiv = _link_function('glProgramUniform3uiv', None, [GLuint, GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.1')
glProgramUniform4d = _link_function('glProgramUniform4d', None, [GLuint, GLint, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 4.1')
glProgramUniform4dv = _link_function('glProgramUniform4dv', None, [GLuint, GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniform4f = _link_function('glProgramUniform4f', None, [GLuint, GLint, GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 4.1')
glProgramUniform4fv = _link_function('glProgramUniform4fv', None, [GLuint, GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniform4i = _link_function('glProgramUniform4i', None, [GLuint, GLint, GLint, GLint, GLint, GLint], requires='OpenGL 4.1')
glProgramUniform4i64ARB = _link_function('glProgramUniform4i64ARB', None, [GLuint, GLint, GLint64, GLint64, GLint64, GLint64], requires='None')
glProgramUniform4i64vARB = _link_function('glProgramUniform4i64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLint64)], requires='None')
glProgramUniform4iv = _link_function('glProgramUniform4iv', None, [GLuint, GLint, GLsizei, POINTER(GLint)], requires='OpenGL 4.1')
glProgramUniform4ui = _link_function('glProgramUniform4ui', None, [GLuint, GLint, GLuint, GLuint, GLuint, GLuint], requires='OpenGL 4.1')
glProgramUniform4ui64ARB = _link_function('glProgramUniform4ui64ARB', None, [GLuint, GLint, GLuint64, GLuint64, GLuint64, GLuint64], requires='None')
glProgramUniform4ui64vARB = _link_function('glProgramUniform4ui64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLuint64)], requires='None')
glProgramUniform4uiv = _link_function('glProgramUniform4uiv', None, [GLuint, GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 4.1')
glProgramUniformHandleui64ARB = _link_function('glProgramUniformHandleui64ARB', None, [GLuint, GLint, GLuint64], requires='None')
glProgramUniformHandleui64vARB = _link_function('glProgramUniformHandleui64vARB', None, [GLuint, GLint, GLsizei, POINTER(GLuint64)], requires='None')
glProgramUniformMatrix2dv = _link_function('glProgramUniformMatrix2dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix2fv = _link_function('glProgramUniformMatrix2fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix2x3dv = _link_function('glProgramUniformMatrix2x3dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix2x3fv = _link_function('glProgramUniformMatrix2x3fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix2x4dv = _link_function('glProgramUniformMatrix2x4dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix2x4fv = _link_function('glProgramUniformMatrix2x4fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix3dv = _link_function('glProgramUniformMatrix3dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix3fv = _link_function('glProgramUniformMatrix3fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix3x2dv = _link_function('glProgramUniformMatrix3x2dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix3x2fv = _link_function('glProgramUniformMatrix3x2fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix3x4dv = _link_function('glProgramUniformMatrix3x4dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix3x4fv = _link_function('glProgramUniformMatrix3x4fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix4dv = _link_function('glProgramUniformMatrix4dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix4fv = _link_function('glProgramUniformMatrix4fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix4x2dv = _link_function('glProgramUniformMatrix4x2dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix4x2fv = _link_function('glProgramUniformMatrix4x2fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProgramUniformMatrix4x3dv = _link_function('glProgramUniformMatrix4x3dv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.1')
glProgramUniformMatrix4x3fv = _link_function('glProgramUniformMatrix4x3fv', None, [GLuint, GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 4.1')
glProvokingVertex = _link_function('glProvokingVertex', None, [GLenum], requires='OpenGL 3.2')
glPushAttrib = _link_function('glPushAttrib', None, [GLbitfield], requires='OpenGL 1.0')
glPushClientAttrib = _link_function('glPushClientAttrib', None, [GLbitfield], requires='OpenGL 1.1')
glPushDebugGroup = _link_function('glPushDebugGroup', None, [GLenum, GLuint, GLsizei, POINTER(GLchar)], requires='OpenGL 4.3')
glPushMatrix = _link_function('glPushMatrix', None, [], requires='OpenGL 1.0')
glPushName = _link_function('glPushName', None, [GLuint], requires='OpenGL 1.0')
glQueryCounter = _link_function('glQueryCounter', None, [GLuint, GLenum], requires='OpenGL 3.3')
glRasterPos2d = _link_function('glRasterPos2d', None, [GLdouble, GLdouble], requires='OpenGL 1.0')
glRasterPos2dv = _link_function('glRasterPos2dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glRasterPos2f = _link_function('glRasterPos2f', None, [GLfloat, GLfloat], requires='OpenGL 1.0')
glRasterPos2fv = _link_function('glRasterPos2fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glRasterPos2i = _link_function('glRasterPos2i', None, [GLint, GLint], requires='OpenGL 1.0')
glRasterPos2iv = _link_function('glRasterPos2iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glRasterPos2s = _link_function('glRasterPos2s', None, [GLshort, GLshort], requires='OpenGL 1.0')
glRasterPos2sv = _link_function('glRasterPos2sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glRasterPos3d = _link_function('glRasterPos3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glRasterPos3dv = _link_function('glRasterPos3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glRasterPos3f = _link_function('glRasterPos3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glRasterPos3fv = _link_function('glRasterPos3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glRasterPos3i = _link_function('glRasterPos3i', None, [GLint, GLint, GLint], requires='OpenGL 1.0')
glRasterPos3iv = _link_function('glRasterPos3iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glRasterPos3s = _link_function('glRasterPos3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glRasterPos3sv = _link_function('glRasterPos3sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glRasterPos4d = _link_function('glRasterPos4d', None, [GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glRasterPos4dv = _link_function('glRasterPos4dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glRasterPos4f = _link_function('glRasterPos4f', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glRasterPos4fv = _link_function('glRasterPos4fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glRasterPos4i = _link_function('glRasterPos4i', None, [GLint, GLint, GLint, GLint], requires='OpenGL 1.0')
glRasterPos4iv = _link_function('glRasterPos4iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glRasterPos4s = _link_function('glRasterPos4s', None, [GLshort, GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glRasterPos4sv = _link_function('glRasterPos4sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glReadBuffer = _link_function('glReadBuffer', None, [GLenum], requires='OpenGL 1.0')
glReadPixels = _link_function('glReadPixels', None, [GLint, GLint, GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.0')
glReadnPixels = _link_function('glReadnPixels', None, [GLint, GLint, GLsizei, GLsizei, GLenum, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.5')
glRectd = _link_function('glRectd', None, [GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glRectdv = _link_function('glRectdv', None, [POINTER(GLdouble), POINTER(GLdouble)], requires='OpenGL 1.0')
glRectf = _link_function('glRectf', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glRectfv = _link_function('glRectfv', None, [POINTER(GLfloat), POINTER(GLfloat)], requires='OpenGL 1.0')
glRecti = _link_function('glRecti', None, [GLint, GLint, GLint, GLint], requires='OpenGL 1.0')
glRectiv = _link_function('glRectiv', None, [POINTER(GLint), POINTER(GLint)], requires='OpenGL 1.0')
glRects = _link_function('glRects', None, [GLshort, GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glRectsv = _link_function('glRectsv', None, [POINTER(GLshort), POINTER(GLshort)], requires='OpenGL 1.0')
glReleaseShaderCompiler = _link_function('glReleaseShaderCompiler', None, [], requires='OpenGL 4.1')
glRenderMode = _link_function('glRenderMode', GLint, [GLenum], requires='OpenGL 1.0')
glRenderbufferStorage = _link_function('glRenderbufferStorage', None, [GLenum, GLenum, GLsizei, GLsizei], requires='OpenGL 3.0')
glRenderbufferStorageEXT = _link_function('glRenderbufferStorageEXT', None, [GLenum, GLenum, GLsizei, GLsizei], requires='None')
glRenderbufferStorageMultisample = _link_function('glRenderbufferStorageMultisample', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei], requires='OpenGL 3.0')
glResumeTransformFeedback = _link_function('glResumeTransformFeedback', None, [], requires='OpenGL 4.0')
glRotated = _link_function('glRotated', None, [GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glRotatef = _link_function('glRotatef', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glSampleCoverage = _link_function('glSampleCoverage', None, [GLfloat, GLboolean], requires='OpenGL 1.3')
glSampleCoverageARB = _link_function('glSampleCoverageARB', None, [GLfloat, GLboolean], requires='None')
glSampleMaski = _link_function('glSampleMaski', None, [GLuint, GLbitfield], requires='OpenGL 3.2')
glSamplerParameterIiv = _link_function('glSamplerParameterIiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 3.3')
glSamplerParameterIuiv = _link_function('glSamplerParameterIuiv', None, [GLuint, GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glSamplerParameterf = _link_function('glSamplerParameterf', None, [GLuint, GLenum, GLfloat], requires='OpenGL 3.3')
glSamplerParameterfv = _link_function('glSamplerParameterfv', None, [GLuint, GLenum, POINTER(GLfloat)], requires='OpenGL 3.3')
glSamplerParameteri = _link_function('glSamplerParameteri', None, [GLuint, GLenum, GLint], requires='OpenGL 3.3')
glSamplerParameteriv = _link_function('glSamplerParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 3.3')
glScaled = _link_function('glScaled', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glScalef = _link_function('glScalef', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glScissor = _link_function('glScissor', None, [GLint, GLint, GLsizei, GLsizei], requires='OpenGL 1.0')
glScissorArrayv = _link_function('glScissorArrayv', None, [GLuint, GLsizei, POINTER(GLint)], requires='OpenGL 4.1')
glScissorIndexed = _link_function('glScissorIndexed', None, [GLuint, GLint, GLint, GLsizei, GLsizei], requires='OpenGL 4.1')
glScissorIndexedv = _link_function('glScissorIndexedv', None, [GLuint, POINTER(GLint)], requires='OpenGL 4.1')
glSecondaryColor3b = _link_function('glSecondaryColor3b', None, [GLbyte, GLbyte, GLbyte], requires='OpenGL 1.4')
glSecondaryColor3bv = _link_function('glSecondaryColor3bv', None, [POINTER(GLbyte)], requires='OpenGL 1.4')
glSecondaryColor3d = _link_function('glSecondaryColor3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.4')
glSecondaryColor3dv = _link_function('glSecondaryColor3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.4')
glSecondaryColor3f = _link_function('glSecondaryColor3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.4')
glSecondaryColor3fv = _link_function('glSecondaryColor3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.4')
glSecondaryColor3i = _link_function('glSecondaryColor3i', None, [GLint, GLint, GLint], requires='OpenGL 1.4')
glSecondaryColor3iv = _link_function('glSecondaryColor3iv', None, [POINTER(GLint)], requires='OpenGL 1.4')
glSecondaryColor3s = _link_function('glSecondaryColor3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.4')
glSecondaryColor3sv = _link_function('glSecondaryColor3sv', None, [POINTER(GLshort)], requires='OpenGL 1.4')
glSecondaryColor3ub = _link_function('glSecondaryColor3ub', None, [GLubyte, GLubyte, GLubyte], requires='OpenGL 1.4')
glSecondaryColor3ubv = _link_function('glSecondaryColor3ubv', None, [POINTER(GLubyte)], requires='OpenGL 1.4')
glSecondaryColor3ui = _link_function('glSecondaryColor3ui', None, [GLuint, GLuint, GLuint], requires='OpenGL 1.4')
glSecondaryColor3uiv = _link_function('glSecondaryColor3uiv', None, [POINTER(GLuint)], requires='OpenGL 1.4')
glSecondaryColor3us = _link_function('glSecondaryColor3us', None, [GLushort, GLushort, GLushort], requires='OpenGL 1.4')
glSecondaryColor3usv = _link_function('glSecondaryColor3usv', None, [POINTER(GLushort)], requires='OpenGL 1.4')
glSecondaryColorP3ui = _link_function('glSecondaryColorP3ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glSecondaryColorP3uiv = _link_function('glSecondaryColorP3uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glSecondaryColorPointer = _link_function('glSecondaryColorPointer', None, [GLint, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.4')
glSelectBuffer = _link_function('glSelectBuffer', None, [GLsizei, POINTER(GLuint)], requires='OpenGL 1.0')
glShadeModel = _link_function('glShadeModel', None, [GLenum], requires='OpenGL 1.0')
glShaderBinary = _link_function('glShaderBinary', None, [GLsizei, POINTER(GLuint), GLenum, POINTER(GLvoid), GLsizei], requires='OpenGL 4.1')
glShaderSource = _link_function('glShaderSource', None, [GLuint, GLsizei, POINTER(POINTER(GLchar)), POINTER(GLint)], requires='OpenGL 2.0')
glShaderStorageBlockBinding = _link_function('glShaderStorageBlockBinding', None, [GLuint, GLuint, GLuint], requires='OpenGL 4.3')
glSpecializeShader = _link_function('glSpecializeShader', None, [GLuint, POINTER(GLchar), GLuint, POINTER(GLuint), POINTER(GLuint)], requires='OpenGL 4.6')
glStencilFunc = _link_function('glStencilFunc', None, [GLenum, GLint, GLuint], requires='OpenGL 1.0')
glStencilFuncSeparate = _link_function('glStencilFuncSeparate', None, [GLenum, GLenum, GLint, GLuint], requires='OpenGL 2.0')
glStencilMask = _link_function('glStencilMask', None, [GLuint], requires='OpenGL 1.0')
glStencilMaskSeparate = _link_function('glStencilMaskSeparate', None, [GLenum, GLuint], requires='OpenGL 2.0')
glStencilOp = _link_function('glStencilOp', None, [GLenum, GLenum, GLenum], requires='OpenGL 1.0')
glStencilOpSeparate = _link_function('glStencilOpSeparate', None, [GLenum, GLenum, GLenum, GLenum], requires='OpenGL 2.0')
glTexBuffer = _link_function('glTexBuffer', None, [GLenum, GLenum, GLuint], requires='OpenGL 3.1')
glTexBufferRange = _link_function('glTexBufferRange', None, [GLenum, GLenum, GLuint, GLintptr, GLsizeiptr], requires='OpenGL 4.3')
glTexCoord1d = _link_function('glTexCoord1d', None, [GLdouble], requires='OpenGL 1.0')
glTexCoord1dv = _link_function('glTexCoord1dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glTexCoord1f = _link_function('glTexCoord1f', None, [GLfloat], requires='OpenGL 1.0')
glTexCoord1fv = _link_function('glTexCoord1fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glTexCoord1i = _link_function('glTexCoord1i', None, [GLint], requires='OpenGL 1.0')
glTexCoord1iv = _link_function('glTexCoord1iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glTexCoord1s = _link_function('glTexCoord1s', None, [GLshort], requires='OpenGL 1.0')
glTexCoord1sv = _link_function('glTexCoord1sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glTexCoord2d = _link_function('glTexCoord2d', None, [GLdouble, GLdouble], requires='OpenGL 1.0')
glTexCoord2dv = _link_function('glTexCoord2dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glTexCoord2f = _link_function('glTexCoord2f', None, [GLfloat, GLfloat], requires='OpenGL 1.0')
glTexCoord2fv = _link_function('glTexCoord2fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glTexCoord2i = _link_function('glTexCoord2i', None, [GLint, GLint], requires='OpenGL 1.0')
glTexCoord2iv = _link_function('glTexCoord2iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glTexCoord2s = _link_function('glTexCoord2s', None, [GLshort, GLshort], requires='OpenGL 1.0')
glTexCoord2sv = _link_function('glTexCoord2sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glTexCoord3d = _link_function('glTexCoord3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glTexCoord3dv = _link_function('glTexCoord3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glTexCoord3f = _link_function('glTexCoord3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glTexCoord3fv = _link_function('glTexCoord3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glTexCoord3i = _link_function('glTexCoord3i', None, [GLint, GLint, GLint], requires='OpenGL 1.0')
glTexCoord3iv = _link_function('glTexCoord3iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glTexCoord3s = _link_function('glTexCoord3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glTexCoord3sv = _link_function('glTexCoord3sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glTexCoord4d = _link_function('glTexCoord4d', None, [GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glTexCoord4dv = _link_function('glTexCoord4dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glTexCoord4f = _link_function('glTexCoord4f', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glTexCoord4fv = _link_function('glTexCoord4fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glTexCoord4i = _link_function('glTexCoord4i', None, [GLint, GLint, GLint, GLint], requires='OpenGL 1.0')
glTexCoord4iv = _link_function('glTexCoord4iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glTexCoord4s = _link_function('glTexCoord4s', None, [GLshort, GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glTexCoord4sv = _link_function('glTexCoord4sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glTexCoordP1ui = _link_function('glTexCoordP1ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glTexCoordP1uiv = _link_function('glTexCoordP1uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glTexCoordP2ui = _link_function('glTexCoordP2ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glTexCoordP2uiv = _link_function('glTexCoordP2uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glTexCoordP3ui = _link_function('glTexCoordP3ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glTexCoordP3uiv = _link_function('glTexCoordP3uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glTexCoordP4ui = _link_function('glTexCoordP4ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glTexCoordP4uiv = _link_function('glTexCoordP4uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glTexCoordPointer = _link_function('glTexCoordPointer', None, [GLint, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glTexEnvf = _link_function('glTexEnvf', None, [GLenum, GLenum, GLfloat], requires='OpenGL 1.0')
glTexEnvfv = _link_function('glTexEnvfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glTexEnvi = _link_function('glTexEnvi', None, [GLenum, GLenum, GLint], requires='OpenGL 1.0')
glTexEnviv = _link_function('glTexEnviv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glTexGend = _link_function('glTexGend', None, [GLenum, GLenum, GLdouble], requires='OpenGL 1.0')
glTexGendv = _link_function('glTexGendv', None, [GLenum, GLenum, POINTER(GLdouble)], requires='OpenGL 1.0')
glTexGenf = _link_function('glTexGenf', None, [GLenum, GLenum, GLfloat], requires='OpenGL 1.0')
glTexGenfv = _link_function('glTexGenfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glTexGeni = _link_function('glTexGeni', None, [GLenum, GLenum, GLint], requires='OpenGL 1.0')
glTexGeniv = _link_function('glTexGeniv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glTexImage1D = _link_function('glTexImage1D', None, [GLenum, GLint, GLint, GLsizei, GLint, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.0')
glTexImage2D = _link_function('glTexImage2D', None, [GLenum, GLint, GLint, GLsizei, GLsizei, GLint, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.0')
glTexImage2DMultisample = _link_function('glTexImage2DMultisample', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei, GLboolean], requires='OpenGL 3.2')
glTexImage3D = _link_function('glTexImage3D', None, [GLenum, GLint, GLint, GLsizei, GLsizei, GLsizei, GLint, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.2')
glTexImage3DMultisample = _link_function('glTexImage3DMultisample', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei, GLsizei, GLboolean], requires='OpenGL 3.2')
glTexParameterIiv = _link_function('glTexParameterIiv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 3.0')
glTexParameterIuiv = _link_function('glTexParameterIuiv', None, [GLenum, GLenum, POINTER(GLuint)], requires='OpenGL 3.0')
glTexParameterf = _link_function('glTexParameterf', None, [GLenum, GLenum, GLfloat], requires='OpenGL 1.0')
glTexParameterfv = _link_function('glTexParameterfv', None, [GLenum, GLenum, POINTER(GLfloat)], requires='OpenGL 1.0')
glTexParameteri = _link_function('glTexParameteri', None, [GLenum, GLenum, GLint], requires='OpenGL 1.0')
glTexParameteriv = _link_function('glTexParameteriv', None, [GLenum, GLenum, POINTER(GLint)], requires='OpenGL 1.0')
glTexStorage1D = _link_function('glTexStorage1D', None, [GLenum, GLsizei, GLenum, GLsizei], requires='OpenGL 4.2')
glTexStorage2D = _link_function('glTexStorage2D', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei], requires='OpenGL 4.2')
glTexStorage2DMultisample = _link_function('glTexStorage2DMultisample', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei, GLboolean], requires='OpenGL 4.3')
glTexStorage3D = _link_function('glTexStorage3D', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei, GLsizei], requires='OpenGL 4.2')
glTexStorage3DMultisample = _link_function('glTexStorage3DMultisample', None, [GLenum, GLsizei, GLenum, GLsizei, GLsizei, GLsizei, GLboolean], requires='OpenGL 4.3')
glTexSubImage1D = _link_function('glTexSubImage1D', None, [GLenum, GLint, GLint, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.1')
glTexSubImage2D = _link_function('glTexSubImage2D', None, [GLenum, GLint, GLint, GLint, GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.1')
glTexSubImage3D = _link_function('glTexSubImage3D', None, [GLenum, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 1.2')
glTextureBarrier = _link_function('glTextureBarrier', None, [], requires='OpenGL 4.5')
glTextureBuffer = _link_function('glTextureBuffer', None, [GLuint, GLenum, GLuint], requires='OpenGL 4.5')
glTextureBufferRange = _link_function('glTextureBufferRange', None, [GLuint, GLenum, GLuint, GLintptr, GLsizeiptr], requires='OpenGL 4.5')
glTextureParameterIiv = _link_function('glTextureParameterIiv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glTextureParameterIuiv = _link_function('glTextureParameterIuiv', None, [GLuint, GLenum, POINTER(GLuint)], requires='OpenGL 4.5')
glTextureParameterf = _link_function('glTextureParameterf', None, [GLuint, GLenum, GLfloat], requires='OpenGL 4.5')
glTextureParameterfv = _link_function('glTextureParameterfv', None, [GLuint, GLenum, POINTER(GLfloat)], requires='OpenGL 4.5')
glTextureParameteri = _link_function('glTextureParameteri', None, [GLuint, GLenum, GLint], requires='OpenGL 4.5')
glTextureParameteriv = _link_function('glTextureParameteriv', None, [GLuint, GLenum, POINTER(GLint)], requires='OpenGL 4.5')
glTextureStorage1D = _link_function('glTextureStorage1D', None, [GLuint, GLsizei, GLenum, GLsizei], requires='OpenGL 4.5')
glTextureStorage2D = _link_function('glTextureStorage2D', None, [GLuint, GLsizei, GLenum, GLsizei, GLsizei], requires='OpenGL 4.5')
glTextureStorage2DMultisample = _link_function('glTextureStorage2DMultisample', None, [GLuint, GLsizei, GLenum, GLsizei, GLsizei, GLboolean], requires='OpenGL 4.5')
glTextureStorage3D = _link_function('glTextureStorage3D', None, [GLuint, GLsizei, GLenum, GLsizei, GLsizei, GLsizei], requires='OpenGL 4.5')
glTextureStorage3DMultisample = _link_function('glTextureStorage3DMultisample', None, [GLuint, GLsizei, GLenum, GLsizei, GLsizei, GLsizei, GLboolean], requires='OpenGL 4.5')
glTextureSubImage1D = _link_function('glTextureSubImage1D', None, [GLuint, GLint, GLint, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.5')
glTextureSubImage2D = _link_function('glTextureSubImage2D', None, [GLuint, GLint, GLint, GLint, GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.5')
glTextureSubImage3D = _link_function('glTextureSubImage3D', None, [GLuint, GLint, GLint, GLint, GLint, GLsizei, GLsizei, GLsizei, GLenum, GLenum, POINTER(GLvoid)], requires='OpenGL 4.5')
glTextureView = _link_function('glTextureView', None, [GLuint, GLenum, GLuint, GLenum, GLuint, GLuint, GLuint, GLuint], requires='OpenGL 4.3')
glTransformFeedbackBufferBase = _link_function('glTransformFeedbackBufferBase', None, [GLuint, GLuint, GLuint], requires='OpenGL 4.5')
glTransformFeedbackBufferRange = _link_function('glTransformFeedbackBufferRange', None, [GLuint, GLuint, GLuint, GLintptr, GLsizeiptr], requires='OpenGL 4.5')
glTransformFeedbackVaryings = _link_function('glTransformFeedbackVaryings', None, [GLuint, GLsizei, POINTER(POINTER(GLchar)), GLenum], requires='OpenGL 3.0')
glTranslated = _link_function('glTranslated', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glTranslatef = _link_function('glTranslatef', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glUniform1d = _link_function('glUniform1d', None, [GLint, GLdouble], requires='OpenGL 4.0')
glUniform1dv = _link_function('glUniform1dv', None, [GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniform1f = _link_function('glUniform1f', None, [GLint, GLfloat], requires='OpenGL 2.0')
glUniform1fv = _link_function('glUniform1fv', None, [GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniform1i = _link_function('glUniform1i', None, [GLint, GLint], requires='OpenGL 2.0')
glUniform1i64ARB = _link_function('glUniform1i64ARB', None, [GLint, GLint64], requires='None')
glUniform1i64vARB = _link_function('glUniform1i64vARB', None, [GLint, GLsizei, POINTER(GLint64)], requires='None')
glUniform1iv = _link_function('glUniform1iv', None, [GLint, GLsizei, POINTER(GLint)], requires='OpenGL 2.0')
glUniform1ui = _link_function('glUniform1ui', None, [GLint, GLuint], requires='OpenGL 3.0')
glUniform1ui64ARB = _link_function('glUniform1ui64ARB', None, [GLint, GLuint64], requires='None')
glUniform1ui64vARB = _link_function('glUniform1ui64vARB', None, [GLint, GLsizei, POINTER(GLuint64)], requires='None')
glUniform1uiv = _link_function('glUniform1uiv', None, [GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glUniform2d = _link_function('glUniform2d', None, [GLint, GLdouble, GLdouble], requires='OpenGL 4.0')
glUniform2dv = _link_function('glUniform2dv', None, [GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniform2f = _link_function('glUniform2f', None, [GLint, GLfloat, GLfloat], requires='OpenGL 2.0')
glUniform2fv = _link_function('glUniform2fv', None, [GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniform2i = _link_function('glUniform2i', None, [GLint, GLint, GLint], requires='OpenGL 2.0')
glUniform2i64ARB = _link_function('glUniform2i64ARB', None, [GLint, GLint64, GLint64], requires='None')
glUniform2i64vARB = _link_function('glUniform2i64vARB', None, [GLint, GLsizei, POINTER(GLint64)], requires='None')
glUniform2iv = _link_function('glUniform2iv', None, [GLint, GLsizei, POINTER(GLint)], requires='OpenGL 2.0')
glUniform2ui = _link_function('glUniform2ui', None, [GLint, GLuint, GLuint], requires='OpenGL 3.0')
glUniform2ui64ARB = _link_function('glUniform2ui64ARB', None, [GLint, GLuint64, GLuint64], requires='None')
glUniform2ui64vARB = _link_function('glUniform2ui64vARB', None, [GLint, GLsizei, POINTER(GLuint64)], requires='None')
glUniform2uiv = _link_function('glUniform2uiv', None, [GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glUniform3d = _link_function('glUniform3d', None, [GLint, GLdouble, GLdouble, GLdouble], requires='OpenGL 4.0')
glUniform3dv = _link_function('glUniform3dv', None, [GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniform3f = _link_function('glUniform3f', None, [GLint, GLfloat, GLfloat, GLfloat], requires='OpenGL 2.0')
glUniform3fv = _link_function('glUniform3fv', None, [GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniform3i = _link_function('glUniform3i', None, [GLint, GLint, GLint, GLint], requires='OpenGL 2.0')
glUniform3i64ARB = _link_function('glUniform3i64ARB', None, [GLint, GLint64, GLint64, GLint64], requires='None')
glUniform3i64vARB = _link_function('glUniform3i64vARB', None, [GLint, GLsizei, POINTER(GLint64)], requires='None')
glUniform3iv = _link_function('glUniform3iv', None, [GLint, GLsizei, POINTER(GLint)], requires='OpenGL 2.0')
glUniform3ui = _link_function('glUniform3ui', None, [GLint, GLuint, GLuint, GLuint], requires='OpenGL 3.0')
glUniform3ui64ARB = _link_function('glUniform3ui64ARB', None, [GLint, GLuint64, GLuint64, GLuint64], requires='None')
glUniform3ui64vARB = _link_function('glUniform3ui64vARB', None, [GLint, GLsizei, POINTER(GLuint64)], requires='None')
glUniform3uiv = _link_function('glUniform3uiv', None, [GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glUniform4d = _link_function('glUniform4d', None, [GLint, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 4.0')
glUniform4dv = _link_function('glUniform4dv', None, [GLint, GLsizei, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniform4f = _link_function('glUniform4f', None, [GLint, GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 2.0')
glUniform4fv = _link_function('glUniform4fv', None, [GLint, GLsizei, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniform4i = _link_function('glUniform4i', None, [GLint, GLint, GLint, GLint, GLint], requires='OpenGL 2.0')
glUniform4i64ARB = _link_function('glUniform4i64ARB', None, [GLint, GLint64, GLint64, GLint64, GLint64], requires='None')
glUniform4i64vARB = _link_function('glUniform4i64vARB', None, [GLint, GLsizei, POINTER(GLint64)], requires='None')
glUniform4iv = _link_function('glUniform4iv', None, [GLint, GLsizei, POINTER(GLint)], requires='OpenGL 2.0')
glUniform4ui = _link_function('glUniform4ui', None, [GLint, GLuint, GLuint, GLuint, GLuint], requires='OpenGL 3.0')
glUniform4ui64ARB = _link_function('glUniform4ui64ARB', None, [GLint, GLuint64, GLuint64, GLuint64, GLuint64], requires='None')
glUniform4ui64vARB = _link_function('glUniform4ui64vARB', None, [GLint, GLsizei, POINTER(GLuint64)], requires='None')
glUniform4uiv = _link_function('glUniform4uiv', None, [GLint, GLsizei, POINTER(GLuint)], requires='OpenGL 3.0')
glUniformBlockBinding = _link_function('glUniformBlockBinding', None, [GLuint, GLuint, GLuint], requires='OpenGL 3.1')
glUniformHandleui64ARB = _link_function('glUniformHandleui64ARB', None, [GLint, GLuint64], requires='None')
glUniformHandleui64vARB = _link_function('glUniformHandleui64vARB', None, [GLint, GLsizei, POINTER(GLuint64)], requires='None')
glUniformMatrix2dv = _link_function('glUniformMatrix2dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix2fv = _link_function('glUniformMatrix2fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniformMatrix2x3dv = _link_function('glUniformMatrix2x3dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix2x3fv = _link_function('glUniformMatrix2x3fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.1')
glUniformMatrix2x4dv = _link_function('glUniformMatrix2x4dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix2x4fv = _link_function('glUniformMatrix2x4fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.1')
glUniformMatrix3dv = _link_function('glUniformMatrix3dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix3fv = _link_function('glUniformMatrix3fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniformMatrix3x2dv = _link_function('glUniformMatrix3x2dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix3x2fv = _link_function('glUniformMatrix3x2fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.1')
glUniformMatrix3x4dv = _link_function('glUniformMatrix3x4dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix3x4fv = _link_function('glUniformMatrix3x4fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.1')
glUniformMatrix4dv = _link_function('glUniformMatrix4dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix4fv = _link_function('glUniformMatrix4fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.0')
glUniformMatrix4x2dv = _link_function('glUniformMatrix4x2dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix4x2fv = _link_function('glUniformMatrix4x2fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.1')
glUniformMatrix4x3dv = _link_function('glUniformMatrix4x3dv', None, [GLint, GLsizei, GLboolean, POINTER(GLdouble)], requires='OpenGL 4.0')
glUniformMatrix4x3fv = _link_function('glUniformMatrix4x3fv', None, [GLint, GLsizei, GLboolean, POINTER(GLfloat)], requires='OpenGL 2.1')
glUniformSubroutinesuiv = _link_function('glUniformSubroutinesuiv', None, [GLenum, GLsizei, POINTER(GLuint)], requires='OpenGL 4.0')
glUnmapBuffer = _link_function('glUnmapBuffer', GLboolean, [GLenum], requires='OpenGL 1.5')
glUnmapNamedBuffer = _link_function('glUnmapNamedBuffer', GLboolean, [GLuint], requires='OpenGL 4.5')
glUseProgram = _link_function('glUseProgram', None, [GLuint], requires='OpenGL 2.0')
glUseProgramStages = _link_function('glUseProgramStages', None, [GLuint, GLbitfield, GLuint], requires='OpenGL 4.1')
glValidateProgram = _link_function('glValidateProgram', None, [GLuint], requires='OpenGL 2.0')
glValidateProgramPipeline = _link_function('glValidateProgramPipeline', None, [GLuint], requires='OpenGL 4.1')
glVertex2d = _link_function('glVertex2d', None, [GLdouble, GLdouble], requires='OpenGL 1.0')
glVertex2dv = _link_function('glVertex2dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glVertex2f = _link_function('glVertex2f', None, [GLfloat, GLfloat], requires='OpenGL 1.0')
glVertex2fv = _link_function('glVertex2fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glVertex2i = _link_function('glVertex2i', None, [GLint, GLint], requires='OpenGL 1.0')
glVertex2iv = _link_function('glVertex2iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glVertex2s = _link_function('glVertex2s', None, [GLshort, GLshort], requires='OpenGL 1.0')
glVertex2sv = _link_function('glVertex2sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glVertex3d = _link_function('glVertex3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glVertex3dv = _link_function('glVertex3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glVertex3f = _link_function('glVertex3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glVertex3fv = _link_function('glVertex3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glVertex3i = _link_function('glVertex3i', None, [GLint, GLint, GLint], requires='OpenGL 1.0')
glVertex3iv = _link_function('glVertex3iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glVertex3s = _link_function('glVertex3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glVertex3sv = _link_function('glVertex3sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glVertex4d = _link_function('glVertex4d', None, [GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 1.0')
glVertex4dv = _link_function('glVertex4dv', None, [POINTER(GLdouble)], requires='OpenGL 1.0')
glVertex4f = _link_function('glVertex4f', None, [GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 1.0')
glVertex4fv = _link_function('glVertex4fv', None, [POINTER(GLfloat)], requires='OpenGL 1.0')
glVertex4i = _link_function('glVertex4i', None, [GLint, GLint, GLint, GLint], requires='OpenGL 1.0')
glVertex4iv = _link_function('glVertex4iv', None, [POINTER(GLint)], requires='OpenGL 1.0')
glVertex4s = _link_function('glVertex4s', None, [GLshort, GLshort, GLshort, GLshort], requires='OpenGL 1.0')
glVertex4sv = _link_function('glVertex4sv', None, [POINTER(GLshort)], requires='OpenGL 1.0')
glVertexArrayAttribBinding = _link_function('glVertexArrayAttribBinding', None, [GLuint, GLuint, GLuint], requires='OpenGL 4.5')
glVertexArrayAttribFormat = _link_function('glVertexArrayAttribFormat', None, [GLuint, GLuint, GLint, GLenum, GLboolean, GLuint], requires='OpenGL 4.5')
glVertexArrayAttribIFormat = _link_function('glVertexArrayAttribIFormat', None, [GLuint, GLuint, GLint, GLenum, GLuint], requires='OpenGL 4.5')
glVertexArrayAttribLFormat = _link_function('glVertexArrayAttribLFormat', None, [GLuint, GLuint, GLint, GLenum, GLuint], requires='OpenGL 4.5')
glVertexArrayBindingDivisor = _link_function('glVertexArrayBindingDivisor', None, [GLuint, GLuint, GLuint], requires='OpenGL 4.5')
glVertexArrayElementBuffer = _link_function('glVertexArrayElementBuffer', None, [GLuint, GLuint], requires='OpenGL 4.5')
glVertexArrayVertexBuffer = _link_function('glVertexArrayVertexBuffer', None, [GLuint, GLuint, GLuint, GLintptr, GLsizei], requires='OpenGL 4.5')
glVertexArrayVertexBuffers = _link_function('glVertexArrayVertexBuffers', None, [GLuint, GLuint, GLsizei, POINTER(GLuint), POINTER(GLintptr), POINTER(GLsizei)], requires='OpenGL 4.5')
glVertexAttrib1d = _link_function('glVertexAttrib1d', None, [GLuint, GLdouble], requires='OpenGL 2.0')
glVertexAttrib1dv = _link_function('glVertexAttrib1dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 2.0')
glVertexAttrib1f = _link_function('glVertexAttrib1f', None, [GLuint, GLfloat], requires='OpenGL 2.0')
glVertexAttrib1fv = _link_function('glVertexAttrib1fv', None, [GLuint, POINTER(GLfloat)], requires='OpenGL 2.0')
glVertexAttrib1s = _link_function('glVertexAttrib1s', None, [GLuint, GLshort], requires='OpenGL 2.0')
glVertexAttrib1sv = _link_function('glVertexAttrib1sv', None, [GLuint, POINTER(GLshort)], requires='OpenGL 2.0')
glVertexAttrib2d = _link_function('glVertexAttrib2d', None, [GLuint, GLdouble, GLdouble], requires='OpenGL 2.0')
glVertexAttrib2dv = _link_function('glVertexAttrib2dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 2.0')
glVertexAttrib2f = _link_function('glVertexAttrib2f', None, [GLuint, GLfloat, GLfloat], requires='OpenGL 2.0')
glVertexAttrib2fv = _link_function('glVertexAttrib2fv', None, [GLuint, POINTER(GLfloat)], requires='OpenGL 2.0')
glVertexAttrib2s = _link_function('glVertexAttrib2s', None, [GLuint, GLshort, GLshort], requires='OpenGL 2.0')
glVertexAttrib2sv = _link_function('glVertexAttrib2sv', None, [GLuint, POINTER(GLshort)], requires='OpenGL 2.0')
glVertexAttrib3d = _link_function('glVertexAttrib3d', None, [GLuint, GLdouble, GLdouble, GLdouble], requires='OpenGL 2.0')
glVertexAttrib3dv = _link_function('glVertexAttrib3dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 2.0')
glVertexAttrib3f = _link_function('glVertexAttrib3f', None, [GLuint, GLfloat, GLfloat, GLfloat], requires='OpenGL 2.0')
glVertexAttrib3fv = _link_function('glVertexAttrib3fv', None, [GLuint, POINTER(GLfloat)], requires='OpenGL 2.0')
glVertexAttrib3s = _link_function('glVertexAttrib3s', None, [GLuint, GLshort, GLshort, GLshort], requires='OpenGL 2.0')
glVertexAttrib3sv = _link_function('glVertexAttrib3sv', None, [GLuint, POINTER(GLshort)], requires='OpenGL 2.0')
glVertexAttrib4Nbv = _link_function('glVertexAttrib4Nbv', None, [GLuint, POINTER(GLbyte)], requires='OpenGL 2.0')
glVertexAttrib4Niv = _link_function('glVertexAttrib4Niv', None, [GLuint, POINTER(GLint)], requires='OpenGL 2.0')
glVertexAttrib4Nsv = _link_function('glVertexAttrib4Nsv', None, [GLuint, POINTER(GLshort)], requires='OpenGL 2.0')
glVertexAttrib4Nub = _link_function('glVertexAttrib4Nub', None, [GLuint, GLubyte, GLubyte, GLubyte, GLubyte], requires='OpenGL 2.0')
glVertexAttrib4Nubv = _link_function('glVertexAttrib4Nubv', None, [GLuint, POINTER(GLubyte)], requires='OpenGL 2.0')
glVertexAttrib4Nuiv = _link_function('glVertexAttrib4Nuiv', None, [GLuint, POINTER(GLuint)], requires='OpenGL 2.0')
glVertexAttrib4Nusv = _link_function('glVertexAttrib4Nusv', None, [GLuint, POINTER(GLushort)], requires='OpenGL 2.0')
glVertexAttrib4bv = _link_function('glVertexAttrib4bv', None, [GLuint, POINTER(GLbyte)], requires='OpenGL 2.0')
glVertexAttrib4d = _link_function('glVertexAttrib4d', None, [GLuint, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 2.0')
glVertexAttrib4dv = _link_function('glVertexAttrib4dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 2.0')
glVertexAttrib4f = _link_function('glVertexAttrib4f', None, [GLuint, GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 2.0')
glVertexAttrib4fv = _link_function('glVertexAttrib4fv', None, [GLuint, POINTER(GLfloat)], requires='OpenGL 2.0')
glVertexAttrib4iv = _link_function('glVertexAttrib4iv', None, [GLuint, POINTER(GLint)], requires='OpenGL 2.0')
glVertexAttrib4s = _link_function('glVertexAttrib4s', None, [GLuint, GLshort, GLshort, GLshort, GLshort], requires='OpenGL 2.0')
glVertexAttrib4sv = _link_function('glVertexAttrib4sv', None, [GLuint, POINTER(GLshort)], requires='OpenGL 2.0')
glVertexAttrib4ubv = _link_function('glVertexAttrib4ubv', None, [GLuint, POINTER(GLubyte)], requires='OpenGL 2.0')
glVertexAttrib4uiv = _link_function('glVertexAttrib4uiv', None, [GLuint, POINTER(GLuint)], requires='OpenGL 2.0')
glVertexAttrib4usv = _link_function('glVertexAttrib4usv', None, [GLuint, POINTER(GLushort)], requires='OpenGL 2.0')
glVertexAttribBinding = _link_function('glVertexAttribBinding', None, [GLuint, GLuint], requires='OpenGL 4.3')
glVertexAttribDivisor = _link_function('glVertexAttribDivisor', None, [GLuint, GLuint], requires='OpenGL 3.3')
glVertexAttribFormat = _link_function('glVertexAttribFormat', None, [GLuint, GLint, GLenum, GLboolean, GLuint], requires='OpenGL 4.3')
glVertexAttribI1i = _link_function('glVertexAttribI1i', None, [GLuint, GLint], requires='OpenGL 3.0')
glVertexAttribI1iv = _link_function('glVertexAttribI1iv', None, [GLuint, POINTER(GLint)], requires='OpenGL 3.0')
glVertexAttribI1ui = _link_function('glVertexAttribI1ui', None, [GLuint, GLuint], requires='OpenGL 3.0')
glVertexAttribI1uiv = _link_function('glVertexAttribI1uiv', None, [GLuint, POINTER(GLuint)], requires='OpenGL 3.0')
glVertexAttribI2i = _link_function('glVertexAttribI2i', None, [GLuint, GLint, GLint], requires='OpenGL 3.0')
glVertexAttribI2iv = _link_function('glVertexAttribI2iv', None, [GLuint, POINTER(GLint)], requires='OpenGL 3.0')
glVertexAttribI2ui = _link_function('glVertexAttribI2ui', None, [GLuint, GLuint, GLuint], requires='OpenGL 3.0')
glVertexAttribI2uiv = _link_function('glVertexAttribI2uiv', None, [GLuint, POINTER(GLuint)], requires='OpenGL 3.0')
glVertexAttribI3i = _link_function('glVertexAttribI3i', None, [GLuint, GLint, GLint, GLint], requires='OpenGL 3.0')
glVertexAttribI3iv = _link_function('glVertexAttribI3iv', None, [GLuint, POINTER(GLint)], requires='OpenGL 3.0')
glVertexAttribI3ui = _link_function('glVertexAttribI3ui', None, [GLuint, GLuint, GLuint, GLuint], requires='OpenGL 3.0')
glVertexAttribI3uiv = _link_function('glVertexAttribI3uiv', None, [GLuint, POINTER(GLuint)], requires='OpenGL 3.0')
glVertexAttribI4bv = _link_function('glVertexAttribI4bv', None, [GLuint, POINTER(GLbyte)], requires='OpenGL 3.0')
glVertexAttribI4i = _link_function('glVertexAttribI4i', None, [GLuint, GLint, GLint, GLint, GLint], requires='OpenGL 3.0')
glVertexAttribI4iv = _link_function('glVertexAttribI4iv', None, [GLuint, POINTER(GLint)], requires='OpenGL 3.0')
glVertexAttribI4sv = _link_function('glVertexAttribI4sv', None, [GLuint, POINTER(GLshort)], requires='OpenGL 3.0')
glVertexAttribI4ubv = _link_function('glVertexAttribI4ubv', None, [GLuint, POINTER(GLubyte)], requires='OpenGL 3.0')
glVertexAttribI4ui = _link_function('glVertexAttribI4ui', None, [GLuint, GLuint, GLuint, GLuint, GLuint], requires='OpenGL 3.0')
glVertexAttribI4uiv = _link_function('glVertexAttribI4uiv', None, [GLuint, POINTER(GLuint)], requires='OpenGL 3.0')
glVertexAttribI4usv = _link_function('glVertexAttribI4usv', None, [GLuint, POINTER(GLushort)], requires='OpenGL 3.0')
glVertexAttribIFormat = _link_function('glVertexAttribIFormat', None, [GLuint, GLint, GLenum, GLuint], requires='OpenGL 4.3')
glVertexAttribIPointer = _link_function('glVertexAttribIPointer', None, [GLuint, GLint, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 3.0')
glVertexAttribL1d = _link_function('glVertexAttribL1d', None, [GLuint, GLdouble], requires='OpenGL 4.1')
glVertexAttribL1dv = _link_function('glVertexAttribL1dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 4.1')
glVertexAttribL1ui64ARB = _link_function('glVertexAttribL1ui64ARB', None, [GLuint, GLuint64EXT], requires='None')
glVertexAttribL1ui64vARB = _link_function('glVertexAttribL1ui64vARB', None, [GLuint, POINTER(GLuint64EXT)], requires='None')
glVertexAttribL2d = _link_function('glVertexAttribL2d', None, [GLuint, GLdouble, GLdouble], requires='OpenGL 4.1')
glVertexAttribL2dv = _link_function('glVertexAttribL2dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 4.1')
glVertexAttribL3d = _link_function('glVertexAttribL3d', None, [GLuint, GLdouble, GLdouble, GLdouble], requires='OpenGL 4.1')
glVertexAttribL3dv = _link_function('glVertexAttribL3dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 4.1')
glVertexAttribL4d = _link_function('glVertexAttribL4d', None, [GLuint, GLdouble, GLdouble, GLdouble, GLdouble], requires='OpenGL 4.1')
glVertexAttribL4dv = _link_function('glVertexAttribL4dv', None, [GLuint, POINTER(GLdouble)], requires='OpenGL 4.1')
glVertexAttribLFormat = _link_function('glVertexAttribLFormat', None, [GLuint, GLint, GLenum, GLuint], requires='OpenGL 4.3')
glVertexAttribLPointer = _link_function('glVertexAttribLPointer', None, [GLuint, GLint, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 4.1')
glVertexAttribP1ui = _link_function('glVertexAttribP1ui', None, [GLuint, GLenum, GLboolean, GLuint], requires='OpenGL 3.3')
glVertexAttribP1uiv = _link_function('glVertexAttribP1uiv', None, [GLuint, GLenum, GLboolean, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexAttribP2ui = _link_function('glVertexAttribP2ui', None, [GLuint, GLenum, GLboolean, GLuint], requires='OpenGL 3.3')
glVertexAttribP2uiv = _link_function('glVertexAttribP2uiv', None, [GLuint, GLenum, GLboolean, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexAttribP3ui = _link_function('glVertexAttribP3ui', None, [GLuint, GLenum, GLboolean, GLuint], requires='OpenGL 3.3')
glVertexAttribP3uiv = _link_function('glVertexAttribP3uiv', None, [GLuint, GLenum, GLboolean, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexAttribP4ui = _link_function('glVertexAttribP4ui', None, [GLuint, GLenum, GLboolean, GLuint], requires='OpenGL 3.3')
glVertexAttribP4uiv = _link_function('glVertexAttribP4uiv', None, [GLuint, GLenum, GLboolean, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexAttribPointer = _link_function('glVertexAttribPointer', None, [GLuint, GLint, GLenum, GLboolean, GLsizei, POINTER(GLvoid)], requires='OpenGL 2.0')
glVertexBindingDivisor = _link_function('glVertexBindingDivisor', None, [GLuint, GLuint], requires='OpenGL 4.3')
glVertexP2ui = _link_function('glVertexP2ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glVertexP2uiv = _link_function('glVertexP2uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexP3ui = _link_function('glVertexP3ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glVertexP3uiv = _link_function('glVertexP3uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexP4ui = _link_function('glVertexP4ui', None, [GLenum, GLuint], requires='OpenGL 3.3')
glVertexP4uiv = _link_function('glVertexP4uiv', None, [GLenum, POINTER(GLuint)], requires='OpenGL 3.3')
glVertexPointer = _link_function('glVertexPointer', None, [GLint, GLenum, GLsizei, POINTER(GLvoid)], requires='OpenGL 1.1')
glViewport = _link_function('glViewport', None, [GLint, GLint, GLsizei, GLsizei], requires='OpenGL 1.0')
glViewportArrayv = _link_function('glViewportArrayv', None, [GLuint, GLsizei, POINTER(GLfloat)], requires='OpenGL 4.1')
glViewportIndexedf = _link_function('glViewportIndexedf', None, [GLuint, GLfloat, GLfloat, GLfloat, GLfloat], requires='OpenGL 4.1')
glViewportIndexedfv = _link_function('glViewportIndexedfv', None, [GLuint, POINTER(GLfloat)], requires='OpenGL 4.1')
glWaitSync = _link_function('glWaitSync', None, [GLsync, GLbitfield, GLuint64], requires='OpenGL 3.2')
glWindowPos2d = _link_function('glWindowPos2d', None, [GLdouble, GLdouble], requires='OpenGL 1.4')
glWindowPos2dv = _link_function('glWindowPos2dv', None, [POINTER(GLdouble)], requires='OpenGL 1.4')
glWindowPos2f = _link_function('glWindowPos2f', None, [GLfloat, GLfloat], requires='OpenGL 1.4')
glWindowPos2fv = _link_function('glWindowPos2fv', None, [POINTER(GLfloat)], requires='OpenGL 1.4')
glWindowPos2i = _link_function('glWindowPos2i', None, [GLint, GLint], requires='OpenGL 1.4')
glWindowPos2iv = _link_function('glWindowPos2iv', None, [POINTER(GLint)], requires='OpenGL 1.4')
glWindowPos2s = _link_function('glWindowPos2s', None, [GLshort, GLshort], requires='OpenGL 1.4')
glWindowPos2sv = _link_function('glWindowPos2sv', None, [POINTER(GLshort)], requires='OpenGL 1.4')
glWindowPos3d = _link_function('glWindowPos3d', None, [GLdouble, GLdouble, GLdouble], requires='OpenGL 1.4')
glWindowPos3dv = _link_function('glWindowPos3dv', None, [POINTER(GLdouble)], requires='OpenGL 1.4')
glWindowPos3f = _link_function('glWindowPos3f', None, [GLfloat, GLfloat, GLfloat], requires='OpenGL 1.4')
glWindowPos3fv = _link_function('glWindowPos3fv', None, [POINTER(GLfloat)], requires='OpenGL 1.4')
glWindowPos3i = _link_function('glWindowPos3i', None, [GLint, GLint, GLint], requires='OpenGL 1.4')
glWindowPos3iv = _link_function('glWindowPos3iv', None, [POINTER(GLint)], requires='OpenGL 1.4')
glWindowPos3s = _link_function('glWindowPos3s', None, [GLshort, GLshort, GLshort], requires='OpenGL 1.4')
glWindowPos3sv = _link_function('glWindowPos3sv', None, [POINTER(GLshort)], requires='OpenGL 1.4')


__all__ = [
    'GLenum',
    'GLboolean',
    'GLbitfield',
    'GLvoid',
    'GLbyte',
    'GLubyte',
    'GLshort',
    'GLushort',
    'GLint',
    'GLuint',
    'GLclampx',
    'GLsizei',
    'GLfloat',
    'GLclampf',
    'GLdouble',
    'GLclampd',
    'GLchar',
    'GLintptr',
    'GLsizeiptr',
    'GLint64',
    'GLuint64',
    'GLuint64EXT',
    'GLsync',
    'GLDEBUGPROC',
    'GL_DEPTH_BUFFER_BIT',
    'GL_STENCIL_BUFFER_BIT',
    'GL_COLOR_BUFFER_BIT',
    'GL_FALSE',
    'GL_TRUE',
    'GL_POINTS',
    'GL_LINES',
    'GL_LINE_LOOP',
    'GL_LINE_STRIP',
    'GL_TRIANGLES',
    'GL_TRIANGLE_STRIP',
    'GL_TRIANGLE_FAN',
    'GL_QUADS',
    'GL_NEVER',
    'GL_LESS',
    'GL_EQUAL',
    'GL_LEQUAL',
    'GL_GREATER',
    'GL_NOTEQUAL',
    'GL_GEQUAL',
    'GL_ALWAYS',
    'GL_ZERO',
    'GL_ONE',
    'GL_SRC_COLOR',
    'GL_ONE_MINUS_SRC_COLOR',
    'GL_SRC_ALPHA',
    'GL_ONE_MINUS_SRC_ALPHA',
    'GL_DST_ALPHA',
    'GL_ONE_MINUS_DST_ALPHA',
    'GL_DST_COLOR',
    'GL_ONE_MINUS_DST_COLOR',
    'GL_SRC_ALPHA_SATURATE',
    'GL_NONE',
    'GL_FRONT_LEFT',
    'GL_FRONT_RIGHT',
    'GL_BACK_LEFT',
    'GL_BACK_RIGHT',
    'GL_FRONT',
    'GL_BACK',
    'GL_LEFT',
    'GL_RIGHT',
    'GL_FRONT_AND_BACK',
    'GL_NO_ERROR',
    'GL_INVALID_ENUM',
    'GL_INVALID_VALUE',
    'GL_INVALID_OPERATION',
    'GL_OUT_OF_MEMORY',
    'GL_CW',
    'GL_CCW',
    'GL_POINT_SIZE',
    'GL_POINT_SIZE_RANGE',
    'GL_POINT_SIZE_GRANULARITY',
    'GL_LINE_SMOOTH',
    'GL_LINE_WIDTH',
    'GL_LINE_WIDTH_RANGE',
    'GL_LINE_WIDTH_GRANULARITY',
    'GL_POLYGON_MODE',
    'GL_POLYGON_SMOOTH',
    'GL_CULL_FACE',
    'GL_CULL_FACE_MODE',
    'GL_FRONT_FACE',
    'GL_DEPTH_RANGE',
    'GL_DEPTH_TEST',
    'GL_DEPTH_WRITEMASK',
    'GL_DEPTH_CLEAR_VALUE',
    'GL_DEPTH_FUNC',
    'GL_STENCIL_TEST',
    'GL_STENCIL_CLEAR_VALUE',
    'GL_STENCIL_FUNC',
    'GL_STENCIL_VALUE_MASK',
    'GL_STENCIL_FAIL',
    'GL_STENCIL_PASS_DEPTH_FAIL',
    'GL_STENCIL_PASS_DEPTH_PASS',
    'GL_STENCIL_REF',
    'GL_STENCIL_WRITEMASK',
    'GL_VIEWPORT',
    'GL_DITHER',
    'GL_BLEND_DST',
    'GL_BLEND_SRC',
    'GL_BLEND',
    'GL_LOGIC_OP_MODE',
    'GL_DRAW_BUFFER',
    'GL_READ_BUFFER',
    'GL_SCISSOR_BOX',
    'GL_SCISSOR_TEST',
    'GL_COLOR_CLEAR_VALUE',
    'GL_COLOR_WRITEMASK',
    'GL_DOUBLEBUFFER',
    'GL_STEREO',
    'GL_LINE_SMOOTH_HINT',
    'GL_POLYGON_SMOOTH_HINT',
    'GL_UNPACK_SWAP_BYTES',
    'GL_UNPACK_LSB_FIRST',
    'GL_UNPACK_ROW_LENGTH',
    'GL_UNPACK_SKIP_ROWS',
    'GL_UNPACK_SKIP_PIXELS',
    'GL_UNPACK_ALIGNMENT',
    'GL_PACK_SWAP_BYTES',
    'GL_PACK_LSB_FIRST',
    'GL_PACK_ROW_LENGTH',
    'GL_PACK_SKIP_ROWS',
    'GL_PACK_SKIP_PIXELS',
    'GL_PACK_ALIGNMENT',
    'GL_MAX_TEXTURE_SIZE',
    'GL_MAX_VIEWPORT_DIMS',
    'GL_SUBPIXEL_BITS',
    'GL_TEXTURE_1D',
    'GL_TEXTURE_2D',
    'GL_TEXTURE_WIDTH',
    'GL_TEXTURE_HEIGHT',
    'GL_TEXTURE_BORDER_COLOR',
    'GL_DONT_CARE',
    'GL_FASTEST',
    'GL_NICEST',
    'GL_BYTE',
    'GL_UNSIGNED_BYTE',
    'GL_SHORT',
    'GL_UNSIGNED_SHORT',
    'GL_INT',
    'GL_UNSIGNED_INT',
    'GL_FLOAT',
    'GL_STACK_OVERFLOW',
    'GL_STACK_UNDERFLOW',
    'GL_CLEAR',
    'GL_AND',
    'GL_AND_REVERSE',
    'GL_COPY',
    'GL_AND_INVERTED',
    'GL_NOOP',
    'GL_XOR',
    'GL_OR',
    'GL_NOR',
    'GL_EQUIV',
    'GL_INVERT',
    'GL_OR_REVERSE',
    'GL_COPY_INVERTED',
    'GL_OR_INVERTED',
    'GL_NAND',
    'GL_SET',
    'GL_TEXTURE',
    'GL_COLOR',
    'GL_DEPTH',
    'GL_STENCIL',
    'GL_STENCIL_INDEX',
    'GL_DEPTH_COMPONENT',
    'GL_RED',
    'GL_GREEN',
    'GL_BLUE',
    'GL_ALPHA',
    'GL_RGB',
    'GL_RGBA',
    'GL_POINT',
    'GL_LINE',
    'GL_FILL',
    'GL_KEEP',
    'GL_REPLACE',
    'GL_INCR',
    'GL_DECR',
    'GL_VENDOR',
    'GL_RENDERER',
    'GL_VERSION',
    'GL_EXTENSIONS',
    'GL_NEAREST',
    'GL_LINEAR',
    'GL_NEAREST_MIPMAP_NEAREST',
    'GL_LINEAR_MIPMAP_NEAREST',
    'GL_NEAREST_MIPMAP_LINEAR',
    'GL_LINEAR_MIPMAP_LINEAR',
    'GL_TEXTURE_MAG_FILTER',
    'GL_TEXTURE_MIN_FILTER',
    'GL_TEXTURE_WRAP_S',
    'GL_TEXTURE_WRAP_T',
    'GL_REPEAT',
    'GL_CURRENT_BIT',
    'GL_POINT_BIT',
    'GL_LINE_BIT',
    'GL_POLYGON_BIT',
    'GL_POLYGON_STIPPLE_BIT',
    'GL_PIXEL_MODE_BIT',
    'GL_LIGHTING_BIT',
    'GL_FOG_BIT',
    'GL_ACCUM_BUFFER_BIT',
    'GL_VIEWPORT_BIT',
    'GL_TRANSFORM_BIT',
    'GL_ENABLE_BIT',
    'GL_HINT_BIT',
    'GL_EVAL_BIT',
    'GL_LIST_BIT',
    'GL_TEXTURE_BIT',
    'GL_SCISSOR_BIT',
    'GL_ALL_ATTRIB_BITS',
    'GL_QUAD_STRIP',
    'GL_POLYGON',
    'GL_ACCUM',
    'GL_LOAD',
    'GL_RETURN',
    'GL_MULT',
    'GL_ADD',
    'GL_AUX0',
    'GL_AUX1',
    'GL_AUX2',
    'GL_AUX3',
    'GL_2D',
    'GL_3D',
    'GL_3D_COLOR',
    'GL_3D_COLOR_TEXTURE',
    'GL_4D_COLOR_TEXTURE',
    'GL_PASS_THROUGH_TOKEN',
    'GL_POINT_TOKEN',
    'GL_LINE_TOKEN',
    'GL_POLYGON_TOKEN',
    'GL_BITMAP_TOKEN',
    'GL_DRAW_PIXEL_TOKEN',
    'GL_COPY_PIXEL_TOKEN',
    'GL_LINE_RESET_TOKEN',
    'GL_EXP',
    'GL_EXP2',
    'GL_COEFF',
    'GL_ORDER',
    'GL_DOMAIN',
    'GL_PIXEL_MAP_I_TO_I',
    'GL_PIXEL_MAP_S_TO_S',
    'GL_PIXEL_MAP_I_TO_R',
    'GL_PIXEL_MAP_I_TO_G',
    'GL_PIXEL_MAP_I_TO_B',
    'GL_PIXEL_MAP_I_TO_A',
    'GL_PIXEL_MAP_R_TO_R',
    'GL_PIXEL_MAP_G_TO_G',
    'GL_PIXEL_MAP_B_TO_B',
    'GL_PIXEL_MAP_A_TO_A',
    'GL_CURRENT_COLOR',
    'GL_CURRENT_INDEX',
    'GL_CURRENT_NORMAL',
    'GL_CURRENT_TEXTURE_COORDS',
    'GL_CURRENT_RASTER_COLOR',
    'GL_CURRENT_RASTER_INDEX',
    'GL_CURRENT_RASTER_TEXTURE_COORDS',
    'GL_CURRENT_RASTER_POSITION',
    'GL_CURRENT_RASTER_POSITION_VALID',
    'GL_CURRENT_RASTER_DISTANCE',
    'GL_POINT_SMOOTH',
    'GL_LINE_STIPPLE',
    'GL_LINE_STIPPLE_PATTERN',
    'GL_LINE_STIPPLE_REPEAT',
    'GL_LIST_MODE',
    'GL_MAX_LIST_NESTING',
    'GL_LIST_BASE',
    'GL_LIST_INDEX',
    'GL_POLYGON_STIPPLE',
    'GL_EDGE_FLAG',
    'GL_LIGHTING',
    'GL_LIGHT_MODEL_LOCAL_VIEWER',
    'GL_LIGHT_MODEL_TWO_SIDE',
    'GL_LIGHT_MODEL_AMBIENT',
    'GL_SHADE_MODEL',
    'GL_COLOR_MATERIAL_FACE',
    'GL_COLOR_MATERIAL_PARAMETER',
    'GL_COLOR_MATERIAL',
    'GL_FOG',
    'GL_FOG_INDEX',
    'GL_FOG_DENSITY',
    'GL_FOG_START',
    'GL_FOG_END',
    'GL_FOG_MODE',
    'GL_FOG_COLOR',
    'GL_ACCUM_CLEAR_VALUE',
    'GL_MATRIX_MODE',
    'GL_NORMALIZE',
    'GL_MODELVIEW_STACK_DEPTH',
    'GL_PROJECTION_STACK_DEPTH',
    'GL_TEXTURE_STACK_DEPTH',
    'GL_MODELVIEW_MATRIX',
    'GL_PROJECTION_MATRIX',
    'GL_TEXTURE_MATRIX',
    'GL_ATTRIB_STACK_DEPTH',
    'GL_ALPHA_TEST',
    'GL_ALPHA_TEST_FUNC',
    'GL_ALPHA_TEST_REF',
    'GL_LOGIC_OP',
    'GL_AUX_BUFFERS',
    'GL_INDEX_CLEAR_VALUE',
    'GL_INDEX_WRITEMASK',
    'GL_INDEX_MODE',
    'GL_RGBA_MODE',
    'GL_RENDER_MODE',
    'GL_PERSPECTIVE_CORRECTION_HINT',
    'GL_POINT_SMOOTH_HINT',
    'GL_FOG_HINT',
    'GL_TEXTURE_GEN_S',
    'GL_TEXTURE_GEN_T',
    'GL_TEXTURE_GEN_R',
    'GL_TEXTURE_GEN_Q',
    'GL_PIXEL_MAP_I_TO_I_SIZE',
    'GL_PIXEL_MAP_S_TO_S_SIZE',
    'GL_PIXEL_MAP_I_TO_R_SIZE',
    'GL_PIXEL_MAP_I_TO_G_SIZE',
    'GL_PIXEL_MAP_I_TO_B_SIZE',
    'GL_PIXEL_MAP_I_TO_A_SIZE',
    'GL_PIXEL_MAP_R_TO_R_SIZE',
    'GL_PIXEL_MAP_G_TO_G_SIZE',
    'GL_PIXEL_MAP_B_TO_B_SIZE',
    'GL_PIXEL_MAP_A_TO_A_SIZE',
    'GL_MAP_COLOR',
    'GL_MAP_STENCIL',
    'GL_INDEX_SHIFT',
    'GL_INDEX_OFFSET',
    'GL_RED_SCALE',
    'GL_RED_BIAS',
    'GL_ZOOM_X',
    'GL_ZOOM_Y',
    'GL_GREEN_SCALE',
    'GL_GREEN_BIAS',
    'GL_BLUE_SCALE',
    'GL_BLUE_BIAS',
    'GL_ALPHA_SCALE',
    'GL_ALPHA_BIAS',
    'GL_DEPTH_SCALE',
    'GL_DEPTH_BIAS',
    'GL_MAX_EVAL_ORDER',
    'GL_MAX_LIGHTS',
    'GL_MAX_CLIP_PLANES',
    'GL_MAX_PIXEL_MAP_TABLE',
    'GL_MAX_ATTRIB_STACK_DEPTH',
    'GL_MAX_MODELVIEW_STACK_DEPTH',
    'GL_MAX_NAME_STACK_DEPTH',
    'GL_MAX_PROJECTION_STACK_DEPTH',
    'GL_MAX_TEXTURE_STACK_DEPTH',
    'GL_INDEX_BITS',
    'GL_RED_BITS',
    'GL_GREEN_BITS',
    'GL_BLUE_BITS',
    'GL_ALPHA_BITS',
    'GL_DEPTH_BITS',
    'GL_STENCIL_BITS',
    'GL_ACCUM_RED_BITS',
    'GL_ACCUM_GREEN_BITS',
    'GL_ACCUM_BLUE_BITS',
    'GL_ACCUM_ALPHA_BITS',
    'GL_NAME_STACK_DEPTH',
    'GL_AUTO_NORMAL',
    'GL_MAP1_COLOR_4',
    'GL_MAP1_INDEX',
    'GL_MAP1_NORMAL',
    'GL_MAP1_TEXTURE_COORD_1',
    'GL_MAP1_TEXTURE_COORD_2',
    'GL_MAP1_TEXTURE_COORD_3',
    'GL_MAP1_TEXTURE_COORD_4',
    'GL_MAP1_VERTEX_3',
    'GL_MAP1_VERTEX_4',
    'GL_MAP2_COLOR_4',
    'GL_MAP2_INDEX',
    'GL_MAP2_NORMAL',
    'GL_MAP2_TEXTURE_COORD_1',
    'GL_MAP2_TEXTURE_COORD_2',
    'GL_MAP2_TEXTURE_COORD_3',
    'GL_MAP2_TEXTURE_COORD_4',
    'GL_MAP2_VERTEX_3',
    'GL_MAP2_VERTEX_4',
    'GL_MAP1_GRID_DOMAIN',
    'GL_MAP1_GRID_SEGMENTS',
    'GL_MAP2_GRID_DOMAIN',
    'GL_MAP2_GRID_SEGMENTS',
    'GL_TEXTURE_COMPONENTS',
    'GL_TEXTURE_BORDER',
    'GL_AMBIENT',
    'GL_DIFFUSE',
    'GL_SPECULAR',
    'GL_POSITION',
    'GL_SPOT_DIRECTION',
    'GL_SPOT_EXPONENT',
    'GL_SPOT_CUTOFF',
    'GL_CONSTANT_ATTENUATION',
    'GL_LINEAR_ATTENUATION',
    'GL_QUADRATIC_ATTENUATION',
    'GL_COMPILE',
    'GL_COMPILE_AND_EXECUTE',
    'GL_2_BYTES',
    'GL_3_BYTES',
    'GL_4_BYTES',
    'GL_EMISSION',
    'GL_SHININESS',
    'GL_AMBIENT_AND_DIFFUSE',
    'GL_COLOR_INDEXES',
    'GL_MODELVIEW',
    'GL_PROJECTION',
    'GL_COLOR_INDEX',
    'GL_LUMINANCE',
    'GL_LUMINANCE_ALPHA',
    'GL_BITMAP',
    'GL_RENDER',
    'GL_FEEDBACK',
    'GL_SELECT',
    'GL_FLAT',
    'GL_SMOOTH',
    'GL_S',
    'GL_T',
    'GL_R',
    'GL_Q',
    'GL_MODULATE',
    'GL_DECAL',
    'GL_TEXTURE_ENV_MODE',
    'GL_TEXTURE_ENV_COLOR',
    'GL_TEXTURE_ENV',
    'GL_EYE_LINEAR',
    'GL_OBJECT_LINEAR',
    'GL_SPHERE_MAP',
    'GL_TEXTURE_GEN_MODE',
    'GL_OBJECT_PLANE',
    'GL_EYE_PLANE',
    'GL_CLAMP',
    'GL_CLIP_PLANE0',
    'GL_CLIP_PLANE1',
    'GL_CLIP_PLANE2',
    'GL_CLIP_PLANE3',
    'GL_CLIP_PLANE4',
    'GL_CLIP_PLANE5',
    'GL_LIGHT0',
    'GL_LIGHT1',
    'GL_LIGHT2',
    'GL_LIGHT3',
    'GL_LIGHT4',
    'GL_LIGHT5',
    'GL_LIGHT6',
    'GL_LIGHT7',
    'GL_COLOR_LOGIC_OP',
    'GL_POLYGON_OFFSET_UNITS',
    'GL_POLYGON_OFFSET_POINT',
    'GL_POLYGON_OFFSET_LINE',
    'GL_POLYGON_OFFSET_FILL',
    'GL_POLYGON_OFFSET_FACTOR',
    'GL_TEXTURE_BINDING_1D',
    'GL_TEXTURE_BINDING_2D',
    'GL_TEXTURE_INTERNAL_FORMAT',
    'GL_TEXTURE_RED_SIZE',
    'GL_TEXTURE_GREEN_SIZE',
    'GL_TEXTURE_BLUE_SIZE',
    'GL_TEXTURE_ALPHA_SIZE',
    'GL_DOUBLE',
    'GL_PROXY_TEXTURE_1D',
    'GL_PROXY_TEXTURE_2D',
    'GL_R3_G3_B2',
    'GL_RGB4',
    'GL_RGB5',
    'GL_RGB8',
    'GL_RGB10',
    'GL_RGB12',
    'GL_RGB16',
    'GL_RGBA2',
    'GL_RGBA4',
    'GL_RGB5_A1',
    'GL_RGBA8',
    'GL_RGB10_A2',
    'GL_RGBA12',
    'GL_RGBA16',
    'GL_CLIENT_PIXEL_STORE_BIT',
    'GL_CLIENT_VERTEX_ARRAY_BIT',
    'GL_CLIENT_ALL_ATTRIB_BITS',
    'GL_VERTEX_ARRAY_POINTER',
    'GL_NORMAL_ARRAY_POINTER',
    'GL_COLOR_ARRAY_POINTER',
    'GL_INDEX_ARRAY_POINTER',
    'GL_TEXTURE_COORD_ARRAY_POINTER',
    'GL_EDGE_FLAG_ARRAY_POINTER',
    'GL_FEEDBACK_BUFFER_POINTER',
    'GL_SELECTION_BUFFER_POINTER',
    'GL_CLIENT_ATTRIB_STACK_DEPTH',
    'GL_INDEX_LOGIC_OP',
    'GL_MAX_CLIENT_ATTRIB_STACK_DEPTH',
    'GL_FEEDBACK_BUFFER_SIZE',
    'GL_FEEDBACK_BUFFER_TYPE',
    'GL_SELECTION_BUFFER_SIZE',
    'GL_VERTEX_ARRAY',
    'GL_NORMAL_ARRAY',
    'GL_COLOR_ARRAY',
    'GL_INDEX_ARRAY',
    'GL_TEXTURE_COORD_ARRAY',
    'GL_EDGE_FLAG_ARRAY',
    'GL_VERTEX_ARRAY_SIZE',
    'GL_VERTEX_ARRAY_TYPE',
    'GL_VERTEX_ARRAY_STRIDE',
    'GL_NORMAL_ARRAY_TYPE',
    'GL_NORMAL_ARRAY_STRIDE',
    'GL_COLOR_ARRAY_SIZE',
    'GL_COLOR_ARRAY_TYPE',
    'GL_COLOR_ARRAY_STRIDE',
    'GL_INDEX_ARRAY_TYPE',
    'GL_INDEX_ARRAY_STRIDE',
    'GL_TEXTURE_COORD_ARRAY_SIZE',
    'GL_TEXTURE_COORD_ARRAY_TYPE',
    'GL_TEXTURE_COORD_ARRAY_STRIDE',
    'GL_EDGE_FLAG_ARRAY_STRIDE',
    'GL_TEXTURE_LUMINANCE_SIZE',
    'GL_TEXTURE_INTENSITY_SIZE',
    'GL_TEXTURE_PRIORITY',
    'GL_TEXTURE_RESIDENT',
    'GL_ALPHA4',
    'GL_ALPHA8',
    'GL_ALPHA12',
    'GL_ALPHA16',
    'GL_LUMINANCE4',
    'GL_LUMINANCE8',
    'GL_LUMINANCE12',
    'GL_LUMINANCE16',
    'GL_LUMINANCE4_ALPHA4',
    'GL_LUMINANCE6_ALPHA2',
    'GL_LUMINANCE8_ALPHA8',
    'GL_LUMINANCE12_ALPHA4',
    'GL_LUMINANCE12_ALPHA12',
    'GL_LUMINANCE16_ALPHA16',
    'GL_INTENSITY',
    'GL_INTENSITY4',
    'GL_INTENSITY8',
    'GL_INTENSITY12',
    'GL_INTENSITY16',
    'GL_V2F',
    'GL_V3F',
    'GL_C4UB_V2F',
    'GL_C4UB_V3F',
    'GL_C3F_V3F',
    'GL_N3F_V3F',
    'GL_C4F_N3F_V3F',
    'GL_T2F_V3F',
    'GL_T4F_V4F',
    'GL_T2F_C4UB_V3F',
    'GL_T2F_C3F_V3F',
    'GL_T2F_N3F_V3F',
    'GL_T2F_C4F_N3F_V3F',
    'GL_T4F_C4F_N3F_V4F',
    'GL_UNSIGNED_BYTE_3_3_2',
    'GL_UNSIGNED_SHORT_4_4_4_4',
    'GL_UNSIGNED_SHORT_5_5_5_1',
    'GL_UNSIGNED_INT_8_8_8_8',
    'GL_UNSIGNED_INT_10_10_10_2',
    'GL_TEXTURE_BINDING_3D',
    'GL_PACK_SKIP_IMAGES',
    'GL_PACK_IMAGE_HEIGHT',
    'GL_UNPACK_SKIP_IMAGES',
    'GL_UNPACK_IMAGE_HEIGHT',
    'GL_TEXTURE_3D',
    'GL_PROXY_TEXTURE_3D',
    'GL_TEXTURE_DEPTH',
    'GL_TEXTURE_WRAP_R',
    'GL_MAX_3D_TEXTURE_SIZE',
    'GL_UNSIGNED_BYTE_2_3_3_REV',
    'GL_UNSIGNED_SHORT_5_6_5',
    'GL_UNSIGNED_SHORT_5_6_5_REV',
    'GL_UNSIGNED_SHORT_4_4_4_4_REV',
    'GL_UNSIGNED_SHORT_1_5_5_5_REV',
    'GL_UNSIGNED_INT_8_8_8_8_REV',
    'GL_UNSIGNED_INT_2_10_10_10_REV',
    'GL_BGR',
    'GL_BGRA',
    'GL_MAX_ELEMENTS_VERTICES',
    'GL_MAX_ELEMENTS_INDICES',
    'GL_CLAMP_TO_EDGE',
    'GL_TEXTURE_MIN_LOD',
    'GL_TEXTURE_MAX_LOD',
    'GL_TEXTURE_BASE_LEVEL',
    'GL_TEXTURE_MAX_LEVEL',
    'GL_SMOOTH_POINT_SIZE_RANGE',
    'GL_SMOOTH_POINT_SIZE_GRANULARITY',
    'GL_SMOOTH_LINE_WIDTH_RANGE',
    'GL_SMOOTH_LINE_WIDTH_GRANULARITY',
    'GL_ALIASED_LINE_WIDTH_RANGE',
    'GL_RESCALE_NORMAL',
    'GL_LIGHT_MODEL_COLOR_CONTROL',
    'GL_SINGLE_COLOR',
    'GL_SEPARATE_SPECULAR_COLOR',
    'GL_ALIASED_POINT_SIZE_RANGE',
    'GL_TEXTURE0',
    'GL_TEXTURE1',
    'GL_TEXTURE2',
    'GL_TEXTURE3',
    'GL_TEXTURE4',
    'GL_TEXTURE5',
    'GL_TEXTURE6',
    'GL_TEXTURE7',
    'GL_TEXTURE8',
    'GL_TEXTURE9',
    'GL_TEXTURE10',
    'GL_TEXTURE11',
    'GL_TEXTURE12',
    'GL_TEXTURE13',
    'GL_TEXTURE14',
    'GL_TEXTURE15',
    'GL_TEXTURE16',
    'GL_TEXTURE17',
    'GL_TEXTURE18',
    'GL_TEXTURE19',
    'GL_TEXTURE20',
    'GL_TEXTURE21',
    'GL_TEXTURE22',
    'GL_TEXTURE23',
    'GL_TEXTURE24',
    'GL_TEXTURE25',
    'GL_TEXTURE26',
    'GL_TEXTURE27',
    'GL_TEXTURE28',
    'GL_TEXTURE29',
    'GL_TEXTURE30',
    'GL_TEXTURE31',
    'GL_ACTIVE_TEXTURE',
    'GL_MULTISAMPLE',
    'GL_SAMPLE_ALPHA_TO_COVERAGE',
    'GL_SAMPLE_ALPHA_TO_ONE',
    'GL_SAMPLE_COVERAGE',
    'GL_SAMPLE_BUFFERS',
    'GL_SAMPLES',
    'GL_SAMPLE_COVERAGE_VALUE',
    'GL_SAMPLE_COVERAGE_INVERT',
    'GL_TEXTURE_CUBE_MAP',
    'GL_TEXTURE_BINDING_CUBE_MAP',
    'GL_TEXTURE_CUBE_MAP_POSITIVE_X',
    'GL_TEXTURE_CUBE_MAP_NEGATIVE_X',
    'GL_TEXTURE_CUBE_MAP_POSITIVE_Y',
    'GL_TEXTURE_CUBE_MAP_NEGATIVE_Y',
    'GL_TEXTURE_CUBE_MAP_POSITIVE_Z',
    'GL_TEXTURE_CUBE_MAP_NEGATIVE_Z',
    'GL_PROXY_TEXTURE_CUBE_MAP',
    'GL_MAX_CUBE_MAP_TEXTURE_SIZE',
    'GL_COMPRESSED_RGB',
    'GL_COMPRESSED_RGBA',
    'GL_TEXTURE_COMPRESSION_HINT',
    'GL_TEXTURE_COMPRESSED_IMAGE_SIZE',
    'GL_TEXTURE_COMPRESSED',
    'GL_NUM_COMPRESSED_TEXTURE_FORMATS',
    'GL_COMPRESSED_TEXTURE_FORMATS',
    'GL_CLAMP_TO_BORDER',
    'GL_CLIENT_ACTIVE_TEXTURE',
    'GL_MAX_TEXTURE_UNITS',
    'GL_TRANSPOSE_MODELVIEW_MATRIX',
    'GL_TRANSPOSE_PROJECTION_MATRIX',
    'GL_TRANSPOSE_TEXTURE_MATRIX',
    'GL_TRANSPOSE_COLOR_MATRIX',
    'GL_MULTISAMPLE_BIT',
    'GL_NORMAL_MAP',
    'GL_REFLECTION_MAP',
    'GL_COMPRESSED_ALPHA',
    'GL_COMPRESSED_LUMINANCE',
    'GL_COMPRESSED_LUMINANCE_ALPHA',
    'GL_COMPRESSED_INTENSITY',
    'GL_COMBINE',
    'GL_COMBINE_RGB',
    'GL_COMBINE_ALPHA',
    'GL_SOURCE0_RGB',
    'GL_SOURCE1_RGB',
    'GL_SOURCE2_RGB',
    'GL_SOURCE0_ALPHA',
    'GL_SOURCE1_ALPHA',
    'GL_SOURCE2_ALPHA',
    'GL_OPERAND0_RGB',
    'GL_OPERAND1_RGB',
    'GL_OPERAND2_RGB',
    'GL_OPERAND0_ALPHA',
    'GL_OPERAND1_ALPHA',
    'GL_OPERAND2_ALPHA',
    'GL_RGB_SCALE',
    'GL_ADD_SIGNED',
    'GL_INTERPOLATE',
    'GL_SUBTRACT',
    'GL_CONSTANT',
    'GL_PRIMARY_COLOR',
    'GL_PREVIOUS',
    'GL_DOT3_RGB',
    'GL_DOT3_RGBA',
    'GL_BLEND_DST_RGB',
    'GL_BLEND_SRC_RGB',
    'GL_BLEND_DST_ALPHA',
    'GL_BLEND_SRC_ALPHA',
    'GL_POINT_FADE_THRESHOLD_SIZE',
    'GL_DEPTH_COMPONENT16',
    'GL_DEPTH_COMPONENT24',
    'GL_DEPTH_COMPONENT32',
    'GL_MIRRORED_REPEAT',
    'GL_MAX_TEXTURE_LOD_BIAS',
    'GL_TEXTURE_LOD_BIAS',
    'GL_INCR_WRAP',
    'GL_DECR_WRAP',
    'GL_TEXTURE_DEPTH_SIZE',
    'GL_TEXTURE_COMPARE_MODE',
    'GL_TEXTURE_COMPARE_FUNC',
    'GL_POINT_SIZE_MIN',
    'GL_POINT_SIZE_MAX',
    'GL_POINT_DISTANCE_ATTENUATION',
    'GL_GENERATE_MIPMAP',
    'GL_GENERATE_MIPMAP_HINT',
    'GL_FOG_COORDINATE_SOURCE',
    'GL_FOG_COORDINATE',
    'GL_FRAGMENT_DEPTH',
    'GL_CURRENT_FOG_COORDINATE',
    'GL_FOG_COORDINATE_ARRAY_TYPE',
    'GL_FOG_COORDINATE_ARRAY_STRIDE',
    'GL_FOG_COORDINATE_ARRAY_POINTER',
    'GL_FOG_COORDINATE_ARRAY',
    'GL_COLOR_SUM',
    'GL_CURRENT_SECONDARY_COLOR',
    'GL_SECONDARY_COLOR_ARRAY_SIZE',
    'GL_SECONDARY_COLOR_ARRAY_TYPE',
    'GL_SECONDARY_COLOR_ARRAY_STRIDE',
    'GL_SECONDARY_COLOR_ARRAY_POINTER',
    'GL_SECONDARY_COLOR_ARRAY',
    'GL_TEXTURE_FILTER_CONTROL',
    'GL_DEPTH_TEXTURE_MODE',
    'GL_COMPARE_R_TO_TEXTURE',
    'GL_BLEND_COLOR',
    'GL_BLEND_EQUATION',
    'GL_CONSTANT_COLOR',
    'GL_ONE_MINUS_CONSTANT_COLOR',
    'GL_CONSTANT_ALPHA',
    'GL_ONE_MINUS_CONSTANT_ALPHA',
    'GL_FUNC_ADD',
    'GL_FUNC_REVERSE_SUBTRACT',
    'GL_FUNC_SUBTRACT',
    'GL_MIN',
    'GL_MAX',
    'GL_BUFFER_SIZE',
    'GL_BUFFER_USAGE',
    'GL_QUERY_COUNTER_BITS',
    'GL_CURRENT_QUERY',
    'GL_QUERY_RESULT',
    'GL_QUERY_RESULT_AVAILABLE',
    'GL_ARRAY_BUFFER',
    'GL_ELEMENT_ARRAY_BUFFER',
    'GL_ARRAY_BUFFER_BINDING',
    'GL_ELEMENT_ARRAY_BUFFER_BINDING',
    'GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING',
    'GL_READ_ONLY',
    'GL_WRITE_ONLY',
    'GL_READ_WRITE',
    'GL_BUFFER_ACCESS',
    'GL_BUFFER_MAPPED',
    'GL_BUFFER_MAP_POINTER',
    'GL_STREAM_DRAW',
    'GL_STREAM_READ',
    'GL_STREAM_COPY',
    'GL_STATIC_DRAW',
    'GL_STATIC_READ',
    'GL_STATIC_COPY',
    'GL_DYNAMIC_DRAW',
    'GL_DYNAMIC_READ',
    'GL_DYNAMIC_COPY',
    'GL_SAMPLES_PASSED',
    'GL_SRC1_ALPHA',
    'GL_VERTEX_ARRAY_BUFFER_BINDING',
    'GL_NORMAL_ARRAY_BUFFER_BINDING',
    'GL_COLOR_ARRAY_BUFFER_BINDING',
    'GL_INDEX_ARRAY_BUFFER_BINDING',
    'GL_TEXTURE_COORD_ARRAY_BUFFER_BINDING',
    'GL_EDGE_FLAG_ARRAY_BUFFER_BINDING',
    'GL_SECONDARY_COLOR_ARRAY_BUFFER_BINDING',
    'GL_FOG_COORDINATE_ARRAY_BUFFER_BINDING',
    'GL_WEIGHT_ARRAY_BUFFER_BINDING',
    'GL_FOG_COORD_SRC',
    'GL_FOG_COORD',
    'GL_CURRENT_FOG_COORD',
    'GL_FOG_COORD_ARRAY_TYPE',
    'GL_FOG_COORD_ARRAY_STRIDE',
    'GL_FOG_COORD_ARRAY_POINTER',
    'GL_FOG_COORD_ARRAY',
    'GL_FOG_COORD_ARRAY_BUFFER_BINDING',
    'GL_SRC0_RGB',
    'GL_SRC1_RGB',
    'GL_SRC2_RGB',
    'GL_SRC0_ALPHA',
    'GL_SRC2_ALPHA',
    'GL_BLEND_EQUATION_RGB',
    'GL_VERTEX_ATTRIB_ARRAY_ENABLED',
    'GL_VERTEX_ATTRIB_ARRAY_SIZE',
    'GL_VERTEX_ATTRIB_ARRAY_STRIDE',
    'GL_VERTEX_ATTRIB_ARRAY_TYPE',
    'GL_CURRENT_VERTEX_ATTRIB',
    'GL_VERTEX_PROGRAM_POINT_SIZE',
    'GL_VERTEX_ATTRIB_ARRAY_POINTER',
    'GL_STENCIL_BACK_FUNC',
    'GL_STENCIL_BACK_FAIL',
    'GL_STENCIL_BACK_PASS_DEPTH_FAIL',
    'GL_STENCIL_BACK_PASS_DEPTH_PASS',
    'GL_MAX_DRAW_BUFFERS',
    'GL_DRAW_BUFFER0',
    'GL_DRAW_BUFFER1',
    'GL_DRAW_BUFFER2',
    'GL_DRAW_BUFFER3',
    'GL_DRAW_BUFFER4',
    'GL_DRAW_BUFFER5',
    'GL_DRAW_BUFFER6',
    'GL_DRAW_BUFFER7',
    'GL_DRAW_BUFFER8',
    'GL_DRAW_BUFFER9',
    'GL_DRAW_BUFFER10',
    'GL_DRAW_BUFFER11',
    'GL_DRAW_BUFFER12',
    'GL_DRAW_BUFFER13',
    'GL_DRAW_BUFFER14',
    'GL_DRAW_BUFFER15',
    'GL_BLEND_EQUATION_ALPHA',
    'GL_MAX_VERTEX_ATTRIBS',
    'GL_VERTEX_ATTRIB_ARRAY_NORMALIZED',
    'GL_MAX_TEXTURE_IMAGE_UNITS',
    'GL_FRAGMENT_SHADER',
    'GL_VERTEX_SHADER',
    'GL_MAX_FRAGMENT_UNIFORM_COMPONENTS',
    'GL_MAX_VERTEX_UNIFORM_COMPONENTS',
    'GL_MAX_VARYING_FLOATS',
    'GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS',
    'GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS',
    'GL_SHADER_TYPE',
    'GL_FLOAT_VEC2',
    'GL_FLOAT_VEC3',
    'GL_FLOAT_VEC4',
    'GL_INT_VEC2',
    'GL_INT_VEC3',
    'GL_INT_VEC4',
    'GL_BOOL',
    'GL_BOOL_VEC2',
    'GL_BOOL_VEC3',
    'GL_BOOL_VEC4',
    'GL_FLOAT_MAT2',
    'GL_FLOAT_MAT3',
    'GL_FLOAT_MAT4',
    'GL_SAMPLER_1D',
    'GL_SAMPLER_2D',
    'GL_SAMPLER_3D',
    'GL_SAMPLER_CUBE',
    'GL_SAMPLER_1D_SHADOW',
    'GL_SAMPLER_2D_SHADOW',
    'GL_DELETE_STATUS',
    'GL_COMPILE_STATUS',
    'GL_LINK_STATUS',
    'GL_VALIDATE_STATUS',
    'GL_INFO_LOG_LENGTH',
    'GL_ATTACHED_SHADERS',
    'GL_ACTIVE_UNIFORMS',
    'GL_ACTIVE_UNIFORM_MAX_LENGTH',
    'GL_SHADER_SOURCE_LENGTH',
    'GL_ACTIVE_ATTRIBUTES',
    'GL_ACTIVE_ATTRIBUTE_MAX_LENGTH',
    'GL_FRAGMENT_SHADER_DERIVATIVE_HINT',
    'GL_SHADING_LANGUAGE_VERSION',
    'GL_CURRENT_PROGRAM',
    'GL_POINT_SPRITE_COORD_ORIGIN',
    'GL_LOWER_LEFT',
    'GL_UPPER_LEFT',
    'GL_STENCIL_BACK_REF',
    'GL_STENCIL_BACK_VALUE_MASK',
    'GL_STENCIL_BACK_WRITEMASK',
    'GL_VERTEX_PROGRAM_TWO_SIDE',
    'GL_POINT_SPRITE',
    'GL_COORD_REPLACE',
    'GL_MAX_TEXTURE_COORDS',
    'GL_PIXEL_PACK_BUFFER',
    'GL_PIXEL_UNPACK_BUFFER',
    'GL_PIXEL_PACK_BUFFER_BINDING',
    'GL_PIXEL_UNPACK_BUFFER_BINDING',
    'GL_FLOAT_MAT2x3',
    'GL_FLOAT_MAT2x4',
    'GL_FLOAT_MAT3x2',
    'GL_FLOAT_MAT3x4',
    'GL_FLOAT_MAT4x2',
    'GL_FLOAT_MAT4x3',
    'GL_SRGB',
    'GL_SRGB8',
    'GL_SRGB_ALPHA',
    'GL_SRGB8_ALPHA8',
    'GL_COMPRESSED_SRGB',
    'GL_COMPRESSED_SRGB_ALPHA',
    'GL_CURRENT_RASTER_SECONDARY_COLOR',
    'GL_SLUMINANCE_ALPHA',
    'GL_SLUMINANCE8_ALPHA8',
    'GL_SLUMINANCE',
    'GL_SLUMINANCE8',
    'GL_COMPRESSED_SLUMINANCE',
    'GL_COMPRESSED_SLUMINANCE_ALPHA',
    'GL_COMPARE_REF_TO_TEXTURE',
    'GL_CLIP_DISTANCE0',
    'GL_CLIP_DISTANCE1',
    'GL_CLIP_DISTANCE2',
    'GL_CLIP_DISTANCE3',
    'GL_CLIP_DISTANCE4',
    'GL_CLIP_DISTANCE5',
    'GL_CLIP_DISTANCE6',
    'GL_CLIP_DISTANCE7',
    'GL_MAX_CLIP_DISTANCES',
    'GL_MAJOR_VERSION',
    'GL_MINOR_VERSION',
    'GL_NUM_EXTENSIONS',
    'GL_CONTEXT_FLAGS',
    'GL_COMPRESSED_RED',
    'GL_COMPRESSED_RG',
    'GL_CONTEXT_FLAG_FORWARD_COMPATIBLE_BIT',
    'GL_RGBA32F',
    'GL_RGB32F',
    'GL_RGBA16F',
    'GL_RGB16F',
    'GL_VERTEX_ATTRIB_ARRAY_INTEGER',
    'GL_MAX_ARRAY_TEXTURE_LAYERS',
    'GL_MIN_PROGRAM_TEXEL_OFFSET',
    'GL_MAX_PROGRAM_TEXEL_OFFSET',
    'GL_CLAMP_READ_COLOR',
    'GL_FIXED_ONLY',
    'GL_MAX_VARYING_COMPONENTS',
    'GL_TEXTURE_1D_ARRAY',
    'GL_PROXY_TEXTURE_1D_ARRAY',
    'GL_TEXTURE_2D_ARRAY',
    'GL_PROXY_TEXTURE_2D_ARRAY',
    'GL_TEXTURE_BINDING_1D_ARRAY',
    'GL_TEXTURE_BINDING_2D_ARRAY',
    'GL_R11F_G11F_B10F',
    'GL_UNSIGNED_INT_10F_11F_11F_REV',
    'GL_RGB9_E5',
    'GL_UNSIGNED_INT_5_9_9_9_REV',
    'GL_TEXTURE_SHARED_SIZE',
    'GL_TRANSFORM_FEEDBACK_VARYING_MAX_LENGTH',
    'GL_TRANSFORM_FEEDBACK_BUFFER_MODE',
    'GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS',
    'GL_TRANSFORM_FEEDBACK_VARYINGS',
    'GL_TRANSFORM_FEEDBACK_BUFFER_START',
    'GL_TRANSFORM_FEEDBACK_BUFFER_SIZE',
    'GL_PRIMITIVES_GENERATED',
    'GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN',
    'GL_RASTERIZER_DISCARD',
    'GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS',
    'GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS',
    'GL_INTERLEAVED_ATTRIBS',
    'GL_SEPARATE_ATTRIBS',
    'GL_TRANSFORM_FEEDBACK_BUFFER',
    'GL_TRANSFORM_FEEDBACK_BUFFER_BINDING',
    'GL_RGBA32UI',
    'GL_RGB32UI',
    'GL_RGBA16UI',
    'GL_RGB16UI',
    'GL_RGBA8UI',
    'GL_RGB8UI',
    'GL_RGBA32I',
    'GL_RGB32I',
    'GL_RGBA16I',
    'GL_RGB16I',
    'GL_RGBA8I',
    'GL_RGB8I',
    'GL_RED_INTEGER',
    'GL_GREEN_INTEGER',
    'GL_BLUE_INTEGER',
    'GL_RGB_INTEGER',
    'GL_RGBA_INTEGER',
    'GL_BGR_INTEGER',
    'GL_BGRA_INTEGER',
    'GL_SAMPLER_1D_ARRAY',
    'GL_SAMPLER_2D_ARRAY',
    'GL_SAMPLER_1D_ARRAY_SHADOW',
    'GL_SAMPLER_2D_ARRAY_SHADOW',
    'GL_SAMPLER_CUBE_SHADOW',
    'GL_UNSIGNED_INT_VEC2',
    'GL_UNSIGNED_INT_VEC3',
    'GL_UNSIGNED_INT_VEC4',
    'GL_INT_SAMPLER_1D',
    'GL_INT_SAMPLER_2D',
    'GL_INT_SAMPLER_3D',
    'GL_INT_SAMPLER_CUBE',
    'GL_INT_SAMPLER_1D_ARRAY',
    'GL_INT_SAMPLER_2D_ARRAY',
    'GL_UNSIGNED_INT_SAMPLER_1D',
    'GL_UNSIGNED_INT_SAMPLER_2D',
    'GL_UNSIGNED_INT_SAMPLER_3D',
    'GL_UNSIGNED_INT_SAMPLER_CUBE',
    'GL_UNSIGNED_INT_SAMPLER_1D_ARRAY',
    'GL_UNSIGNED_INT_SAMPLER_2D_ARRAY',
    'GL_QUERY_WAIT',
    'GL_QUERY_NO_WAIT',
    'GL_QUERY_BY_REGION_WAIT',
    'GL_QUERY_BY_REGION_NO_WAIT',
    'GL_BUFFER_ACCESS_FLAGS',
    'GL_BUFFER_MAP_LENGTH',
    'GL_BUFFER_MAP_OFFSET',
    'GL_DEPTH_COMPONENT32F',
    'GL_DEPTH32F_STENCIL8',
    'GL_FLOAT_32_UNSIGNED_INT_24_8_REV',
    'GL_INVALID_FRAMEBUFFER_OPERATION',
    'GL_FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING',
    'GL_FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE',
    'GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE',
    'GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE',
    'GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE',
    'GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE',
    'GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE',
    'GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE',
    'GL_FRAMEBUFFER_DEFAULT',
    'GL_FRAMEBUFFER_UNDEFINED',
    'GL_DEPTH_STENCIL_ATTACHMENT',
    'GL_MAX_RENDERBUFFER_SIZE',
    'GL_DEPTH_STENCIL',
    'GL_UNSIGNED_INT_24_8',
    'GL_DEPTH24_STENCIL8',
    'GL_TEXTURE_STENCIL_SIZE',
    'GL_TEXTURE_RED_TYPE',
    'GL_TEXTURE_GREEN_TYPE',
    'GL_TEXTURE_BLUE_TYPE',
    'GL_TEXTURE_ALPHA_TYPE',
    'GL_TEXTURE_DEPTH_TYPE',
    'GL_UNSIGNED_NORMALIZED',
    'GL_FRAMEBUFFER_BINDING',
    'GL_DRAW_FRAMEBUFFER_BINDING',
    'GL_RENDERBUFFER_BINDING',
    'GL_READ_FRAMEBUFFER',
    'GL_DRAW_FRAMEBUFFER',
    'GL_READ_FRAMEBUFFER_BINDING',
    'GL_RENDERBUFFER_SAMPLES',
    'GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE',
    'GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME',
    'GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL',
    'GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE',
    'GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER',
    'GL_FRAMEBUFFER_COMPLETE',
    'GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT',
    'GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT',
    'GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER',
    'GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER',
    'GL_FRAMEBUFFER_UNSUPPORTED',
    'GL_MAX_COLOR_ATTACHMENTS',
    'GL_COLOR_ATTACHMENT0',
    'GL_COLOR_ATTACHMENT1',
    'GL_COLOR_ATTACHMENT2',
    'GL_COLOR_ATTACHMENT3',
    'GL_COLOR_ATTACHMENT4',
    'GL_COLOR_ATTACHMENT5',
    'GL_COLOR_ATTACHMENT6',
    'GL_COLOR_ATTACHMENT7',
    'GL_COLOR_ATTACHMENT8',
    'GL_COLOR_ATTACHMENT9',
    'GL_COLOR_ATTACHMENT10',
    'GL_COLOR_ATTACHMENT11',
    'GL_COLOR_ATTACHMENT12',
    'GL_COLOR_ATTACHMENT13',
    'GL_COLOR_ATTACHMENT14',
    'GL_COLOR_ATTACHMENT15',
    'GL_COLOR_ATTACHMENT16',
    'GL_COLOR_ATTACHMENT17',
    'GL_COLOR_ATTACHMENT18',
    'GL_COLOR_ATTACHMENT19',
    'GL_COLOR_ATTACHMENT20',
    'GL_COLOR_ATTACHMENT21',
    'GL_COLOR_ATTACHMENT22',
    'GL_COLOR_ATTACHMENT23',
    'GL_COLOR_ATTACHMENT24',
    'GL_COLOR_ATTACHMENT25',
    'GL_COLOR_ATTACHMENT26',
    'GL_COLOR_ATTACHMENT27',
    'GL_COLOR_ATTACHMENT28',
    'GL_COLOR_ATTACHMENT29',
    'GL_COLOR_ATTACHMENT30',
    'GL_COLOR_ATTACHMENT31',
    'GL_DEPTH_ATTACHMENT',
    'GL_STENCIL_ATTACHMENT',
    'GL_FRAMEBUFFER',
    'GL_RENDERBUFFER',
    'GL_RENDERBUFFER_WIDTH',
    'GL_RENDERBUFFER_HEIGHT',
    'GL_RENDERBUFFER_INTERNAL_FORMAT',
    'GL_STENCIL_INDEX1',
    'GL_STENCIL_INDEX4',
    'GL_STENCIL_INDEX8',
    'GL_STENCIL_INDEX16',
    'GL_RENDERBUFFER_RED_SIZE',
    'GL_RENDERBUFFER_GREEN_SIZE',
    'GL_RENDERBUFFER_BLUE_SIZE',
    'GL_RENDERBUFFER_ALPHA_SIZE',
    'GL_RENDERBUFFER_DEPTH_SIZE',
    'GL_RENDERBUFFER_STENCIL_SIZE',
    'GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE',
    'GL_MAX_SAMPLES',
    'GL_INDEX',
    'GL_TEXTURE_LUMINANCE_TYPE',
    'GL_TEXTURE_INTENSITY_TYPE',
    'GL_FRAMEBUFFER_SRGB',
    'GL_HALF_FLOAT',
    'GL_MAP_READ_BIT',
    'GL_MAP_WRITE_BIT',
    'GL_MAP_INVALIDATE_RANGE_BIT',
    'GL_MAP_INVALIDATE_BUFFER_BIT',
    'GL_MAP_FLUSH_EXPLICIT_BIT',
    'GL_MAP_UNSYNCHRONIZED_BIT',
    'GL_COMPRESSED_RED_RGTC1',
    'GL_COMPRESSED_SIGNED_RED_RGTC1',
    'GL_COMPRESSED_RG_RGTC2',
    'GL_COMPRESSED_SIGNED_RG_RGTC2',
    'GL_RG',
    'GL_RG_INTEGER',
    'GL_R8',
    'GL_R16',
    'GL_RG8',
    'GL_RG16',
    'GL_R16F',
    'GL_R32F',
    'GL_RG16F',
    'GL_RG32F',
    'GL_R8I',
    'GL_R8UI',
    'GL_R16I',
    'GL_R16UI',
    'GL_R32I',
    'GL_R32UI',
    'GL_RG8I',
    'GL_RG8UI',
    'GL_RG16I',
    'GL_RG16UI',
    'GL_RG32I',
    'GL_RG32UI',
    'GL_VERTEX_ARRAY_BINDING',
    'GL_CLAMP_VERTEX_COLOR',
    'GL_CLAMP_FRAGMENT_COLOR',
    'GL_ALPHA_INTEGER',
    'GL_SAMPLER_2D_RECT',
    'GL_SAMPLER_2D_RECT_SHADOW',
    'GL_SAMPLER_BUFFER',
    'GL_INT_SAMPLER_2D_RECT',
    'GL_INT_SAMPLER_BUFFER',
    'GL_UNSIGNED_INT_SAMPLER_2D_RECT',
    'GL_UNSIGNED_INT_SAMPLER_BUFFER',
    'GL_TEXTURE_BUFFER',
    'GL_MAX_TEXTURE_BUFFER_SIZE',
    'GL_TEXTURE_BINDING_BUFFER',
    'GL_TEXTURE_BUFFER_DATA_STORE_BINDING',
    'GL_TEXTURE_RECTANGLE',
    'GL_TEXTURE_BINDING_RECTANGLE',
    'GL_PROXY_TEXTURE_RECTANGLE',
    'GL_MAX_RECTANGLE_TEXTURE_SIZE',
    'GL_R8_SNORM',
    'GL_RG8_SNORM',
    'GL_RGB8_SNORM',
    'GL_RGBA8_SNORM',
    'GL_R16_SNORM',
    'GL_RG16_SNORM',
    'GL_RGB16_SNORM',
    'GL_RGBA16_SNORM',
    'GL_SIGNED_NORMALIZED',
    'GL_PRIMITIVE_RESTART',
    'GL_PRIMITIVE_RESTART_INDEX',
    'GL_COPY_READ_BUFFER',
    'GL_COPY_WRITE_BUFFER',
    'GL_UNIFORM_BUFFER',
    'GL_UNIFORM_BUFFER_BINDING',
    'GL_UNIFORM_BUFFER_START',
    'GL_UNIFORM_BUFFER_SIZE',
    'GL_MAX_VERTEX_UNIFORM_BLOCKS',
    'GL_MAX_GEOMETRY_UNIFORM_BLOCKS',
    'GL_MAX_FRAGMENT_UNIFORM_BLOCKS',
    'GL_MAX_COMBINED_UNIFORM_BLOCKS',
    'GL_MAX_UNIFORM_BUFFER_BINDINGS',
    'GL_MAX_UNIFORM_BLOCK_SIZE',
    'GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS',
    'GL_MAX_COMBINED_GEOMETRY_UNIFORM_COMPONENTS',
    'GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS',
    'GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT',
    'GL_ACTIVE_UNIFORM_BLOCK_MAX_NAME_LENGTH',
    'GL_ACTIVE_UNIFORM_BLOCKS',
    'GL_UNIFORM_TYPE',
    'GL_UNIFORM_SIZE',
    'GL_UNIFORM_NAME_LENGTH',
    'GL_UNIFORM_BLOCK_INDEX',
    'GL_UNIFORM_OFFSET',
    'GL_UNIFORM_ARRAY_STRIDE',
    'GL_UNIFORM_MATRIX_STRIDE',
    'GL_UNIFORM_IS_ROW_MAJOR',
    'GL_UNIFORM_BLOCK_BINDING',
    'GL_UNIFORM_BLOCK_DATA_SIZE',
    'GL_UNIFORM_BLOCK_NAME_LENGTH',
    'GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS',
    'GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_GEOMETRY_SHADER',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER',
    'GL_INVALID_INDEX',
    'GL_CONTEXT_CORE_PROFILE_BIT',
    'GL_CONTEXT_COMPATIBILITY_PROFILE_BIT',
    'GL_LINES_ADJACENCY',
    'GL_LINE_STRIP_ADJACENCY',
    'GL_TRIANGLES_ADJACENCY',
    'GL_TRIANGLE_STRIP_ADJACENCY',
    'GL_PROGRAM_POINT_SIZE',
    'GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS',
    'GL_FRAMEBUFFER_ATTACHMENT_LAYERED',
    'GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS',
    'GL_GEOMETRY_SHADER',
    'GL_GEOMETRY_VERTICES_OUT',
    'GL_GEOMETRY_INPUT_TYPE',
    'GL_GEOMETRY_OUTPUT_TYPE',
    'GL_MAX_GEOMETRY_UNIFORM_COMPONENTS',
    'GL_MAX_GEOMETRY_OUTPUT_VERTICES',
    'GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS',
    'GL_MAX_VERTEX_OUTPUT_COMPONENTS',
    'GL_MAX_GEOMETRY_INPUT_COMPONENTS',
    'GL_MAX_GEOMETRY_OUTPUT_COMPONENTS',
    'GL_MAX_FRAGMENT_INPUT_COMPONENTS',
    'GL_CONTEXT_PROFILE_MASK',
    'GL_DEPTH_CLAMP',
    'GL_QUADS_FOLLOW_PROVOKING_VERTEX_CONVENTION',
    'GL_FIRST_VERTEX_CONVENTION',
    'GL_LAST_VERTEX_CONVENTION',
    'GL_PROVOKING_VERTEX',
    'GL_TEXTURE_CUBE_MAP_SEAMLESS',
    'GL_MAX_SERVER_WAIT_TIMEOUT',
    'GL_OBJECT_TYPE',
    'GL_SYNC_CONDITION',
    'GL_SYNC_STATUS',
    'GL_SYNC_FLAGS',
    'GL_SYNC_FENCE',
    'GL_SYNC_GPU_COMMANDS_COMPLETE',
    'GL_UNSIGNALED',
    'GL_SIGNALED',
    'GL_ALREADY_SIGNALED',
    'GL_TIMEOUT_EXPIRED',
    'GL_CONDITION_SATISFIED',
    'GL_WAIT_FAILED',
    'GL_TIMEOUT_IGNORED',
    'GL_SYNC_FLUSH_COMMANDS_BIT',
    'GL_SAMPLE_POSITION',
    'GL_SAMPLE_MASK',
    'GL_SAMPLE_MASK_VALUE',
    'GL_MAX_SAMPLE_MASK_WORDS',
    'GL_TEXTURE_2D_MULTISAMPLE',
    'GL_PROXY_TEXTURE_2D_MULTISAMPLE',
    'GL_TEXTURE_2D_MULTISAMPLE_ARRAY',
    'GL_PROXY_TEXTURE_2D_MULTISAMPLE_ARRAY',
    'GL_TEXTURE_BINDING_2D_MULTISAMPLE',
    'GL_TEXTURE_BINDING_2D_MULTISAMPLE_ARRAY',
    'GL_TEXTURE_SAMPLES',
    'GL_TEXTURE_FIXED_SAMPLE_LOCATIONS',
    'GL_SAMPLER_2D_MULTISAMPLE',
    'GL_INT_SAMPLER_2D_MULTISAMPLE',
    'GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE',
    'GL_SAMPLER_2D_MULTISAMPLE_ARRAY',
    'GL_INT_SAMPLER_2D_MULTISAMPLE_ARRAY',
    'GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE_ARRAY',
    'GL_MAX_COLOR_TEXTURE_SAMPLES',
    'GL_MAX_DEPTH_TEXTURE_SAMPLES',
    'GL_MAX_INTEGER_SAMPLES',
    'GL_VERTEX_ATTRIB_ARRAY_DIVISOR',
    'GL_SRC1_COLOR',
    'GL_ONE_MINUS_SRC1_COLOR',
    'GL_ONE_MINUS_SRC1_ALPHA',
    'GL_MAX_DUAL_SOURCE_DRAW_BUFFERS',
    'GL_ANY_SAMPLES_PASSED',
    'GL_SAMPLER_BINDING',
    'GL_RGB10_A2UI',
    'GL_TEXTURE_SWIZZLE_R',
    'GL_TEXTURE_SWIZZLE_G',
    'GL_TEXTURE_SWIZZLE_B',
    'GL_TEXTURE_SWIZZLE_A',
    'GL_TEXTURE_SWIZZLE_RGBA',
    'GL_TIME_ELAPSED',
    'GL_TIMESTAMP',
    'GL_INT_2_10_10_10_REV',
    'GL_SAMPLE_SHADING',
    'GL_MIN_SAMPLE_SHADING_VALUE',
    'GL_MIN_PROGRAM_TEXTURE_GATHER_OFFSET',
    'GL_MAX_PROGRAM_TEXTURE_GATHER_OFFSET',
    'GL_TEXTURE_CUBE_MAP_ARRAY',
    'GL_TEXTURE_BINDING_CUBE_MAP_ARRAY',
    'GL_PROXY_TEXTURE_CUBE_MAP_ARRAY',
    'GL_SAMPLER_CUBE_MAP_ARRAY',
    'GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW',
    'GL_INT_SAMPLER_CUBE_MAP_ARRAY',
    'GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY',
    'GL_DRAW_INDIRECT_BUFFER',
    'GL_DRAW_INDIRECT_BUFFER_BINDING',
    'GL_GEOMETRY_SHADER_INVOCATIONS',
    'GL_MAX_GEOMETRY_SHADER_INVOCATIONS',
    'GL_MIN_FRAGMENT_INTERPOLATION_OFFSET',
    'GL_MAX_FRAGMENT_INTERPOLATION_OFFSET',
    'GL_FRAGMENT_INTERPOLATION_OFFSET_BITS',
    'GL_MAX_VERTEX_STREAMS',
    'GL_DOUBLE_VEC2',
    'GL_DOUBLE_VEC3',
    'GL_DOUBLE_VEC4',
    'GL_DOUBLE_MAT2',
    'GL_DOUBLE_MAT3',
    'GL_DOUBLE_MAT4',
    'GL_DOUBLE_MAT2x3',
    'GL_DOUBLE_MAT2x4',
    'GL_DOUBLE_MAT3x2',
    'GL_DOUBLE_MAT3x4',
    'GL_DOUBLE_MAT4x2',
    'GL_DOUBLE_MAT4x3',
    'GL_ACTIVE_SUBROUTINES',
    'GL_ACTIVE_SUBROUTINE_UNIFORMS',
    'GL_ACTIVE_SUBROUTINE_UNIFORM_LOCATIONS',
    'GL_ACTIVE_SUBROUTINE_MAX_LENGTH',
    'GL_ACTIVE_SUBROUTINE_UNIFORM_MAX_LENGTH',
    'GL_MAX_SUBROUTINES',
    'GL_MAX_SUBROUTINE_UNIFORM_LOCATIONS',
    'GL_NUM_COMPATIBLE_SUBROUTINES',
    'GL_COMPATIBLE_SUBROUTINES',
    'GL_PATCHES',
    'GL_PATCH_VERTICES',
    'GL_PATCH_DEFAULT_INNER_LEVEL',
    'GL_PATCH_DEFAULT_OUTER_LEVEL',
    'GL_TESS_CONTROL_OUTPUT_VERTICES',
    'GL_TESS_GEN_MODE',
    'GL_TESS_GEN_SPACING',
    'GL_TESS_GEN_VERTEX_ORDER',
    'GL_TESS_GEN_POINT_MODE',
    'GL_ISOLINES',
    'GL_FRACTIONAL_ODD',
    'GL_FRACTIONAL_EVEN',
    'GL_MAX_PATCH_VERTICES',
    'GL_MAX_TESS_GEN_LEVEL',
    'GL_MAX_TESS_CONTROL_UNIFORM_COMPONENTS',
    'GL_MAX_TESS_EVALUATION_UNIFORM_COMPONENTS',
    'GL_MAX_TESS_CONTROL_TEXTURE_IMAGE_UNITS',
    'GL_MAX_TESS_EVALUATION_TEXTURE_IMAGE_UNITS',
    'GL_MAX_TESS_CONTROL_OUTPUT_COMPONENTS',
    'GL_MAX_TESS_PATCH_COMPONENTS',
    'GL_MAX_TESS_CONTROL_TOTAL_OUTPUT_COMPONENTS',
    'GL_MAX_TESS_EVALUATION_OUTPUT_COMPONENTS',
    'GL_MAX_TESS_CONTROL_UNIFORM_BLOCKS',
    'GL_MAX_TESS_EVALUATION_UNIFORM_BLOCKS',
    'GL_MAX_TESS_CONTROL_INPUT_COMPONENTS',
    'GL_MAX_TESS_EVALUATION_INPUT_COMPONENTS',
    'GL_MAX_COMBINED_TESS_CONTROL_UNIFORM_COMPONENTS',
    'GL_MAX_COMBINED_TESS_EVALUATION_UNIFORM_COMPONENTS',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_CONTROL_SHADER',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_EVALUATION_SHADER',
    'GL_TESS_EVALUATION_SHADER',
    'GL_TESS_CONTROL_SHADER',
    'GL_TRANSFORM_FEEDBACK',
    'GL_TRANSFORM_FEEDBACK_BUFFER_PAUSED',
    'GL_TRANSFORM_FEEDBACK_BUFFER_ACTIVE',
    'GL_TRANSFORM_FEEDBACK_BINDING',
    'GL_MAX_TRANSFORM_FEEDBACK_BUFFERS',
    'GL_FIXED',
    'GL_IMPLEMENTATION_COLOR_READ_TYPE',
    'GL_IMPLEMENTATION_COLOR_READ_FORMAT',
    'GL_LOW_FLOAT',
    'GL_MEDIUM_FLOAT',
    'GL_HIGH_FLOAT',
    'GL_LOW_INT',
    'GL_MEDIUM_INT',
    'GL_HIGH_INT',
    'GL_SHADER_COMPILER',
    'GL_SHADER_BINARY_FORMATS',
    'GL_NUM_SHADER_BINARY_FORMATS',
    'GL_MAX_VERTEX_UNIFORM_VECTORS',
    'GL_MAX_VARYING_VECTORS',
    'GL_MAX_FRAGMENT_UNIFORM_VECTORS',
    'GL_RGB565',
    'GL_PROGRAM_BINARY_RETRIEVABLE_HINT',
    'GL_PROGRAM_BINARY_LENGTH',
    'GL_NUM_PROGRAM_BINARY_FORMATS',
    'GL_PROGRAM_BINARY_FORMATS',
    'GL_VERTEX_SHADER_BIT',
    'GL_FRAGMENT_SHADER_BIT',
    'GL_GEOMETRY_SHADER_BIT',
    'GL_TESS_CONTROL_SHADER_BIT',
    'GL_TESS_EVALUATION_SHADER_BIT',
    'GL_ALL_SHADER_BITS',
    'GL_PROGRAM_SEPARABLE',
    'GL_ACTIVE_PROGRAM',
    'GL_PROGRAM_PIPELINE_BINDING',
    'GL_MAX_VIEWPORTS',
    'GL_VIEWPORT_SUBPIXEL_BITS',
    'GL_VIEWPORT_BOUNDS_RANGE',
    'GL_LAYER_PROVOKING_VERTEX',
    'GL_VIEWPORT_INDEX_PROVOKING_VERTEX',
    'GL_UNDEFINED_VERTEX',
    'GL_COPY_READ_BUFFER_BINDING',
    'GL_COPY_WRITE_BUFFER_BINDING',
    'GL_TRANSFORM_FEEDBACK_ACTIVE',
    'GL_TRANSFORM_FEEDBACK_PAUSED',
    'GL_UNPACK_COMPRESSED_BLOCK_WIDTH',
    'GL_UNPACK_COMPRESSED_BLOCK_HEIGHT',
    'GL_UNPACK_COMPRESSED_BLOCK_DEPTH',
    'GL_UNPACK_COMPRESSED_BLOCK_SIZE',
    'GL_PACK_COMPRESSED_BLOCK_WIDTH',
    'GL_PACK_COMPRESSED_BLOCK_HEIGHT',
    'GL_PACK_COMPRESSED_BLOCK_DEPTH',
    'GL_PACK_COMPRESSED_BLOCK_SIZE',
    'GL_NUM_SAMPLE_COUNTS',
    'GL_MIN_MAP_BUFFER_ALIGNMENT',
    'GL_ATOMIC_COUNTER_BUFFER',
    'GL_ATOMIC_COUNTER_BUFFER_BINDING',
    'GL_ATOMIC_COUNTER_BUFFER_START',
    'GL_ATOMIC_COUNTER_BUFFER_SIZE',
    'GL_ATOMIC_COUNTER_BUFFER_DATA_SIZE',
    'GL_ATOMIC_COUNTER_BUFFER_ACTIVE_ATOMIC_COUNTERS',
    'GL_ATOMIC_COUNTER_BUFFER_ACTIVE_ATOMIC_COUNTER_INDICES',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_VERTEX_SHADER',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TESS_CONTROL_SHADER',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TESS_EVALUATION_SHADER',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_GEOMETRY_SHADER',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_FRAGMENT_SHADER',
    'GL_MAX_VERTEX_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_TESS_CONTROL_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_TESS_EVALUATION_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_GEOMETRY_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_FRAGMENT_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_COMBINED_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_VERTEX_ATOMIC_COUNTERS',
    'GL_MAX_TESS_CONTROL_ATOMIC_COUNTERS',
    'GL_MAX_TESS_EVALUATION_ATOMIC_COUNTERS',
    'GL_MAX_GEOMETRY_ATOMIC_COUNTERS',
    'GL_MAX_FRAGMENT_ATOMIC_COUNTERS',
    'GL_MAX_COMBINED_ATOMIC_COUNTERS',
    'GL_MAX_ATOMIC_COUNTER_BUFFER_SIZE',
    'GL_MAX_ATOMIC_COUNTER_BUFFER_BINDINGS',
    'GL_ACTIVE_ATOMIC_COUNTER_BUFFERS',
    'GL_UNIFORM_ATOMIC_COUNTER_BUFFER_INDEX',
    'GL_UNSIGNED_INT_ATOMIC_COUNTER',
    'GL_VERTEX_ATTRIB_ARRAY_BARRIER_BIT',
    'GL_ELEMENT_ARRAY_BARRIER_BIT',
    'GL_UNIFORM_BARRIER_BIT',
    'GL_TEXTURE_FETCH_BARRIER_BIT',
    'GL_SHADER_IMAGE_ACCESS_BARRIER_BIT',
    'GL_COMMAND_BARRIER_BIT',
    'GL_PIXEL_BUFFER_BARRIER_BIT',
    'GL_TEXTURE_UPDATE_BARRIER_BIT',
    'GL_BUFFER_UPDATE_BARRIER_BIT',
    'GL_FRAMEBUFFER_BARRIER_BIT',
    'GL_TRANSFORM_FEEDBACK_BARRIER_BIT',
    'GL_ATOMIC_COUNTER_BARRIER_BIT',
    'GL_ALL_BARRIER_BITS',
    'GL_MAX_IMAGE_UNITS',
    'GL_MAX_COMBINED_IMAGE_UNITS_AND_FRAGMENT_OUTPUTS',
    'GL_IMAGE_BINDING_NAME',
    'GL_IMAGE_BINDING_LEVEL',
    'GL_IMAGE_BINDING_LAYERED',
    'GL_IMAGE_BINDING_LAYER',
    'GL_IMAGE_BINDING_ACCESS',
    'GL_IMAGE_1D',
    'GL_IMAGE_2D',
    'GL_IMAGE_3D',
    'GL_IMAGE_2D_RECT',
    'GL_IMAGE_CUBE',
    'GL_IMAGE_BUFFER',
    'GL_IMAGE_1D_ARRAY',
    'GL_IMAGE_2D_ARRAY',
    'GL_IMAGE_CUBE_MAP_ARRAY',
    'GL_IMAGE_2D_MULTISAMPLE',
    'GL_IMAGE_2D_MULTISAMPLE_ARRAY',
    'GL_INT_IMAGE_1D',
    'GL_INT_IMAGE_2D',
    'GL_INT_IMAGE_3D',
    'GL_INT_IMAGE_2D_RECT',
    'GL_INT_IMAGE_CUBE',
    'GL_INT_IMAGE_BUFFER',
    'GL_INT_IMAGE_1D_ARRAY',
    'GL_INT_IMAGE_2D_ARRAY',
    'GL_INT_IMAGE_CUBE_MAP_ARRAY',
    'GL_INT_IMAGE_2D_MULTISAMPLE',
    'GL_INT_IMAGE_2D_MULTISAMPLE_ARRAY',
    'GL_UNSIGNED_INT_IMAGE_1D',
    'GL_UNSIGNED_INT_IMAGE_2D',
    'GL_UNSIGNED_INT_IMAGE_3D',
    'GL_UNSIGNED_INT_IMAGE_2D_RECT',
    'GL_UNSIGNED_INT_IMAGE_CUBE',
    'GL_UNSIGNED_INT_IMAGE_BUFFER',
    'GL_UNSIGNED_INT_IMAGE_1D_ARRAY',
    'GL_UNSIGNED_INT_IMAGE_2D_ARRAY',
    'GL_UNSIGNED_INT_IMAGE_CUBE_MAP_ARRAY',
    'GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE',
    'GL_UNSIGNED_INT_IMAGE_2D_MULTISAMPLE_ARRAY',
    'GL_MAX_IMAGE_SAMPLES',
    'GL_IMAGE_BINDING_FORMAT',
    'GL_IMAGE_FORMAT_COMPATIBILITY_TYPE',
    'GL_IMAGE_FORMAT_COMPATIBILITY_BY_SIZE',
    'GL_IMAGE_FORMAT_COMPATIBILITY_BY_CLASS',
    'GL_MAX_VERTEX_IMAGE_UNIFORMS',
    'GL_MAX_TESS_CONTROL_IMAGE_UNIFORMS',
    'GL_MAX_TESS_EVALUATION_IMAGE_UNIFORMS',
    'GL_MAX_GEOMETRY_IMAGE_UNIFORMS',
    'GL_MAX_FRAGMENT_IMAGE_UNIFORMS',
    'GL_MAX_COMBINED_IMAGE_UNIFORMS',
    'GL_COMPRESSED_RGBA_BPTC_UNORM',
    'GL_COMPRESSED_SRGB_ALPHA_BPTC_UNORM',
    'GL_COMPRESSED_RGB_BPTC_SIGNED_FLOAT',
    'GL_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT',
    'GL_TEXTURE_IMMUTABLE_FORMAT',
    'GL_NUM_SHADING_LANGUAGE_VERSIONS',
    'GL_VERTEX_ATTRIB_ARRAY_LONG',
    'GL_COMPRESSED_RGB8_ETC2',
    'GL_COMPRESSED_SRGB8_ETC2',
    'GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2',
    'GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2',
    'GL_COMPRESSED_RGBA8_ETC2_EAC',
    'GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC',
    'GL_COMPRESSED_R11_EAC',
    'GL_COMPRESSED_SIGNED_R11_EAC',
    'GL_COMPRESSED_RG11_EAC',
    'GL_COMPRESSED_SIGNED_RG11_EAC',
    'GL_PRIMITIVE_RESTART_FIXED_INDEX',
    'GL_ANY_SAMPLES_PASSED_CONSERVATIVE',
    'GL_MAX_ELEMENT_INDEX',
    'GL_COMPUTE_SHADER',
    'GL_MAX_COMPUTE_UNIFORM_BLOCKS',
    'GL_MAX_COMPUTE_TEXTURE_IMAGE_UNITS',
    'GL_MAX_COMPUTE_IMAGE_UNIFORMS',
    'GL_MAX_COMPUTE_SHARED_MEMORY_SIZE',
    'GL_MAX_COMPUTE_UNIFORM_COMPONENTS',
    'GL_MAX_COMPUTE_ATOMIC_COUNTER_BUFFERS',
    'GL_MAX_COMPUTE_ATOMIC_COUNTERS',
    'GL_MAX_COMBINED_COMPUTE_UNIFORM_COMPONENTS',
    'GL_MAX_COMPUTE_WORK_GROUP_INVOCATIONS',
    'GL_MAX_COMPUTE_WORK_GROUP_COUNT',
    'GL_MAX_COMPUTE_WORK_GROUP_SIZE',
    'GL_COMPUTE_WORK_GROUP_SIZE',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_COMPUTE_SHADER',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_COMPUTE_SHADER',
    'GL_DISPATCH_INDIRECT_BUFFER',
    'GL_DISPATCH_INDIRECT_BUFFER_BINDING',
    'GL_COMPUTE_SHADER_BIT',
    'GL_DEBUG_OUTPUT_SYNCHRONOUS',
    'GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH',
    'GL_DEBUG_CALLBACK_FUNCTION',
    'GL_DEBUG_CALLBACK_USER_PARAM',
    'GL_DEBUG_SOURCE_API',
    'GL_DEBUG_SOURCE_WINDOW_SYSTEM',
    'GL_DEBUG_SOURCE_SHADER_COMPILER',
    'GL_DEBUG_SOURCE_THIRD_PARTY',
    'GL_DEBUG_SOURCE_APPLICATION',
    'GL_DEBUG_SOURCE_OTHER',
    'GL_DEBUG_TYPE_ERROR',
    'GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR',
    'GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR',
    'GL_DEBUG_TYPE_PORTABILITY',
    'GL_DEBUG_TYPE_PERFORMANCE',
    'GL_DEBUG_TYPE_OTHER',
    'GL_MAX_DEBUG_MESSAGE_LENGTH',
    'GL_MAX_DEBUG_LOGGED_MESSAGES',
    'GL_DEBUG_LOGGED_MESSAGES',
    'GL_DEBUG_SEVERITY_HIGH',
    'GL_DEBUG_SEVERITY_MEDIUM',
    'GL_DEBUG_SEVERITY_LOW',
    'GL_DEBUG_TYPE_MARKER',
    'GL_DEBUG_TYPE_PUSH_GROUP',
    'GL_DEBUG_TYPE_POP_GROUP',
    'GL_DEBUG_SEVERITY_NOTIFICATION',
    'GL_MAX_DEBUG_GROUP_STACK_DEPTH',
    'GL_DEBUG_GROUP_STACK_DEPTH',
    'GL_BUFFER',
    'GL_SHADER',
    'GL_PROGRAM',
    'GL_QUERY',
    'GL_PROGRAM_PIPELINE',
    'GL_SAMPLER',
    'GL_MAX_LABEL_LENGTH',
    'GL_DEBUG_OUTPUT',
    'GL_CONTEXT_FLAG_DEBUG_BIT',
    'GL_MAX_UNIFORM_LOCATIONS',
    'GL_FRAMEBUFFER_DEFAULT_WIDTH',
    'GL_FRAMEBUFFER_DEFAULT_HEIGHT',
    'GL_FRAMEBUFFER_DEFAULT_LAYERS',
    'GL_FRAMEBUFFER_DEFAULT_SAMPLES',
    'GL_FRAMEBUFFER_DEFAULT_FIXED_SAMPLE_LOCATIONS',
    'GL_MAX_FRAMEBUFFER_WIDTH',
    'GL_MAX_FRAMEBUFFER_HEIGHT',
    'GL_MAX_FRAMEBUFFER_LAYERS',
    'GL_MAX_FRAMEBUFFER_SAMPLES',
    'GL_INTERNALFORMAT_SUPPORTED',
    'GL_INTERNALFORMAT_PREFERRED',
    'GL_INTERNALFORMAT_RED_SIZE',
    'GL_INTERNALFORMAT_GREEN_SIZE',
    'GL_INTERNALFORMAT_BLUE_SIZE',
    'GL_INTERNALFORMAT_ALPHA_SIZE',
    'GL_INTERNALFORMAT_DEPTH_SIZE',
    'GL_INTERNALFORMAT_STENCIL_SIZE',
    'GL_INTERNALFORMAT_SHARED_SIZE',
    'GL_INTERNALFORMAT_RED_TYPE',
    'GL_INTERNALFORMAT_GREEN_TYPE',
    'GL_INTERNALFORMAT_BLUE_TYPE',
    'GL_INTERNALFORMAT_ALPHA_TYPE',
    'GL_INTERNALFORMAT_DEPTH_TYPE',
    'GL_INTERNALFORMAT_STENCIL_TYPE',
    'GL_MAX_WIDTH',
    'GL_MAX_HEIGHT',
    'GL_MAX_DEPTH',
    'GL_MAX_LAYERS',
    'GL_MAX_COMBINED_DIMENSIONS',
    'GL_COLOR_COMPONENTS',
    'GL_DEPTH_COMPONENTS',
    'GL_STENCIL_COMPONENTS',
    'GL_COLOR_RENDERABLE',
    'GL_DEPTH_RENDERABLE',
    'GL_STENCIL_RENDERABLE',
    'GL_FRAMEBUFFER_RENDERABLE',
    'GL_FRAMEBUFFER_RENDERABLE_LAYERED',
    'GL_FRAMEBUFFER_BLEND',
    'GL_READ_PIXELS',
    'GL_READ_PIXELS_FORMAT',
    'GL_READ_PIXELS_TYPE',
    'GL_TEXTURE_IMAGE_FORMAT',
    'GL_TEXTURE_IMAGE_TYPE',
    'GL_GET_TEXTURE_IMAGE_FORMAT',
    'GL_GET_TEXTURE_IMAGE_TYPE',
    'GL_MIPMAP',
    'GL_MANUAL_GENERATE_MIPMAP',
    'GL_AUTO_GENERATE_MIPMAP',
    'GL_COLOR_ENCODING',
    'GL_SRGB_READ',
    'GL_SRGB_WRITE',
    'GL_FILTER',
    'GL_VERTEX_TEXTURE',
    'GL_TESS_CONTROL_TEXTURE',
    'GL_TESS_EVALUATION_TEXTURE',
    'GL_GEOMETRY_TEXTURE',
    'GL_FRAGMENT_TEXTURE',
    'GL_COMPUTE_TEXTURE',
    'GL_TEXTURE_SHADOW',
    'GL_TEXTURE_GATHER',
    'GL_TEXTURE_GATHER_SHADOW',
    'GL_SHADER_IMAGE_LOAD',
    'GL_SHADER_IMAGE_STORE',
    'GL_SHADER_IMAGE_ATOMIC',
    'GL_IMAGE_TEXEL_SIZE',
    'GL_IMAGE_COMPATIBILITY_CLASS',
    'GL_IMAGE_PIXEL_FORMAT',
    'GL_IMAGE_PIXEL_TYPE',
    'GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_TEST',
    'GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_TEST',
    'GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_WRITE',
    'GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_WRITE',
    'GL_TEXTURE_COMPRESSED_BLOCK_WIDTH',
    'GL_TEXTURE_COMPRESSED_BLOCK_HEIGHT',
    'GL_TEXTURE_COMPRESSED_BLOCK_SIZE',
    'GL_CLEAR_BUFFER',
    'GL_TEXTURE_VIEW',
    'GL_VIEW_COMPATIBILITY_CLASS',
    'GL_FULL_SUPPORT',
    'GL_CAVEAT_SUPPORT',
    'GL_IMAGE_CLASS_4_X_32',
    'GL_IMAGE_CLASS_2_X_32',
    'GL_IMAGE_CLASS_1_X_32',
    'GL_IMAGE_CLASS_4_X_16',
    'GL_IMAGE_CLASS_2_X_16',
    'GL_IMAGE_CLASS_1_X_16',
    'GL_IMAGE_CLASS_4_X_8',
    'GL_IMAGE_CLASS_2_X_8',
    'GL_IMAGE_CLASS_1_X_8',
    'GL_IMAGE_CLASS_11_11_10',
    'GL_IMAGE_CLASS_10_10_10_2',
    'GL_VIEW_CLASS_128_BITS',
    'GL_VIEW_CLASS_96_BITS',
    'GL_VIEW_CLASS_64_BITS',
    'GL_VIEW_CLASS_48_BITS',
    'GL_VIEW_CLASS_32_BITS',
    'GL_VIEW_CLASS_24_BITS',
    'GL_VIEW_CLASS_16_BITS',
    'GL_VIEW_CLASS_8_BITS',
    'GL_VIEW_CLASS_S3TC_DXT1_RGB',
    'GL_VIEW_CLASS_S3TC_DXT1_RGBA',
    'GL_VIEW_CLASS_S3TC_DXT3_RGBA',
    'GL_VIEW_CLASS_S3TC_DXT5_RGBA',
    'GL_VIEW_CLASS_RGTC1_RED',
    'GL_VIEW_CLASS_RGTC2_RG',
    'GL_VIEW_CLASS_BPTC_UNORM',
    'GL_VIEW_CLASS_BPTC_FLOAT',
    'GL_UNIFORM',
    'GL_UNIFORM_BLOCK',
    'GL_PROGRAM_INPUT',
    'GL_PROGRAM_OUTPUT',
    'GL_BUFFER_VARIABLE',
    'GL_SHADER_STORAGE_BLOCK',
    'GL_VERTEX_SUBROUTINE',
    'GL_TESS_CONTROL_SUBROUTINE',
    'GL_TESS_EVALUATION_SUBROUTINE',
    'GL_GEOMETRY_SUBROUTINE',
    'GL_FRAGMENT_SUBROUTINE',
    'GL_COMPUTE_SUBROUTINE',
    'GL_VERTEX_SUBROUTINE_UNIFORM',
    'GL_TESS_CONTROL_SUBROUTINE_UNIFORM',
    'GL_TESS_EVALUATION_SUBROUTINE_UNIFORM',
    'GL_GEOMETRY_SUBROUTINE_UNIFORM',
    'GL_FRAGMENT_SUBROUTINE_UNIFORM',
    'GL_COMPUTE_SUBROUTINE_UNIFORM',
    'GL_TRANSFORM_FEEDBACK_VARYING',
    'GL_ACTIVE_RESOURCES',
    'GL_MAX_NAME_LENGTH',
    'GL_MAX_NUM_ACTIVE_VARIABLES',
    'GL_MAX_NUM_COMPATIBLE_SUBROUTINES',
    'GL_NAME_LENGTH',
    'GL_TYPE',
    'GL_ARRAY_SIZE',
    'GL_OFFSET',
    'GL_BLOCK_INDEX',
    'GL_ARRAY_STRIDE',
    'GL_MATRIX_STRIDE',
    'GL_IS_ROW_MAJOR',
    'GL_ATOMIC_COUNTER_BUFFER_INDEX',
    'GL_BUFFER_BINDING',
    'GL_BUFFER_DATA_SIZE',
    'GL_NUM_ACTIVE_VARIABLES',
    'GL_ACTIVE_VARIABLES',
    'GL_REFERENCED_BY_VERTEX_SHADER',
    'GL_REFERENCED_BY_TESS_CONTROL_SHADER',
    'GL_REFERENCED_BY_TESS_EVALUATION_SHADER',
    'GL_REFERENCED_BY_GEOMETRY_SHADER',
    'GL_REFERENCED_BY_FRAGMENT_SHADER',
    'GL_REFERENCED_BY_COMPUTE_SHADER',
    'GL_TOP_LEVEL_ARRAY_SIZE',
    'GL_TOP_LEVEL_ARRAY_STRIDE',
    'GL_LOCATION',
    'GL_LOCATION_INDEX',
    'GL_IS_PER_PATCH',
    'GL_SHADER_STORAGE_BUFFER',
    'GL_SHADER_STORAGE_BUFFER_BINDING',
    'GL_SHADER_STORAGE_BUFFER_START',
    'GL_SHADER_STORAGE_BUFFER_SIZE',
    'GL_MAX_VERTEX_SHADER_STORAGE_BLOCKS',
    'GL_MAX_GEOMETRY_SHADER_STORAGE_BLOCKS',
    'GL_MAX_TESS_CONTROL_SHADER_STORAGE_BLOCKS',
    'GL_MAX_TESS_EVALUATION_SHADER_STORAGE_BLOCKS',
    'GL_MAX_FRAGMENT_SHADER_STORAGE_BLOCKS',
    'GL_MAX_COMPUTE_SHADER_STORAGE_BLOCKS',
    'GL_MAX_COMBINED_SHADER_STORAGE_BLOCKS',
    'GL_MAX_SHADER_STORAGE_BUFFER_BINDINGS',
    'GL_MAX_SHADER_STORAGE_BLOCK_SIZE',
    'GL_SHADER_STORAGE_BUFFER_OFFSET_ALIGNMENT',
    'GL_SHADER_STORAGE_BARRIER_BIT',
    'GL_MAX_COMBINED_SHADER_OUTPUT_RESOURCES',
    'GL_DEPTH_STENCIL_TEXTURE_MODE',
    'GL_TEXTURE_BUFFER_OFFSET',
    'GL_TEXTURE_BUFFER_SIZE',
    'GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT',
    'GL_TEXTURE_VIEW_MIN_LEVEL',
    'GL_TEXTURE_VIEW_NUM_LEVELS',
    'GL_TEXTURE_VIEW_MIN_LAYER',
    'GL_TEXTURE_VIEW_NUM_LAYERS',
    'GL_TEXTURE_IMMUTABLE_LEVELS',
    'GL_VERTEX_ATTRIB_BINDING',
    'GL_VERTEX_ATTRIB_RELATIVE_OFFSET',
    'GL_VERTEX_BINDING_DIVISOR',
    'GL_VERTEX_BINDING_OFFSET',
    'GL_VERTEX_BINDING_STRIDE',
    'GL_MAX_VERTEX_ATTRIB_RELATIVE_OFFSET',
    'GL_MAX_VERTEX_ATTRIB_BINDINGS',
    'GL_VERTEX_BINDING_BUFFER',
    'GL_DISPLAY_LIST',
    'GL_MAX_VERTEX_ATTRIB_STRIDE',
    'GL_PRIMITIVE_RESTART_FOR_PATCHES_SUPPORTED',
    'GL_TEXTURE_BUFFER_BINDING',
    'GL_MAP_PERSISTENT_BIT',
    'GL_MAP_COHERENT_BIT',
    'GL_DYNAMIC_STORAGE_BIT',
    'GL_CLIENT_STORAGE_BIT',
    'GL_CLIENT_MAPPED_BUFFER_BARRIER_BIT',
    'GL_BUFFER_IMMUTABLE_STORAGE',
    'GL_BUFFER_STORAGE_FLAGS',
    'GL_CLEAR_TEXTURE',
    'GL_LOCATION_COMPONENT',
    'GL_TRANSFORM_FEEDBACK_BUFFER_INDEX',
    'GL_TRANSFORM_FEEDBACK_BUFFER_STRIDE',
    'GL_QUERY_BUFFER',
    'GL_QUERY_BUFFER_BARRIER_BIT',
    'GL_QUERY_BUFFER_BINDING',
    'GL_QUERY_RESULT_NO_WAIT',
    'GL_MIRROR_CLAMP_TO_EDGE',
    'GL_CONTEXT_LOST',
    'GL_NEGATIVE_ONE_TO_ONE',
    'GL_ZERO_TO_ONE',
    'GL_CLIP_ORIGIN',
    'GL_CLIP_DEPTH_MODE',
    'GL_QUERY_WAIT_INVERTED',
    'GL_QUERY_NO_WAIT_INVERTED',
    'GL_QUERY_BY_REGION_WAIT_INVERTED',
    'GL_QUERY_BY_REGION_NO_WAIT_INVERTED',
    'GL_MAX_CULL_DISTANCES',
    'GL_MAX_COMBINED_CLIP_AND_CULL_DISTANCES',
    'GL_TEXTURE_TARGET',
    'GL_QUERY_TARGET',
    'GL_GUILTY_CONTEXT_RESET',
    'GL_INNOCENT_CONTEXT_RESET',
    'GL_UNKNOWN_CONTEXT_RESET',
    'GL_RESET_NOTIFICATION_STRATEGY',
    'GL_LOSE_CONTEXT_ON_RESET',
    'GL_NO_RESET_NOTIFICATION',
    'GL_CONTEXT_FLAG_ROBUST_ACCESS_BIT',
    'GL_COLOR_TABLE',
    'GL_POST_CONVOLUTION_COLOR_TABLE',
    'GL_POST_COLOR_MATRIX_COLOR_TABLE',
    'GL_PROXY_COLOR_TABLE',
    'GL_PROXY_POST_CONVOLUTION_COLOR_TABLE',
    'GL_PROXY_POST_COLOR_MATRIX_COLOR_TABLE',
    'GL_CONVOLUTION_1D',
    'GL_CONVOLUTION_2D',
    'GL_SEPARABLE_2D',
    'GL_HISTOGRAM',
    'GL_PROXY_HISTOGRAM',
    'GL_MINMAX',
    'GL_CONTEXT_RELEASE_BEHAVIOR',
    'GL_CONTEXT_RELEASE_BEHAVIOR_FLUSH',
    'GL_SHADER_BINARY_FORMAT_SPIR_V',
    'GL_SPIR_V_BINARY',
    'GL_PARAMETER_BUFFER',
    'GL_PARAMETER_BUFFER_BINDING',
    'GL_CONTEXT_FLAG_NO_ERROR_BIT',
    'GL_VERTICES_SUBMITTED',
    'GL_PRIMITIVES_SUBMITTED',
    'GL_VERTEX_SHADER_INVOCATIONS',
    'GL_TESS_CONTROL_SHADER_PATCHES',
    'GL_TESS_EVALUATION_SHADER_INVOCATIONS',
    'GL_GEOMETRY_SHADER_PRIMITIVES_EMITTED',
    'GL_FRAGMENT_SHADER_INVOCATIONS',
    'GL_COMPUTE_SHADER_INVOCATIONS',
    'GL_CLIPPING_INPUT_PRIMITIVES',
    'GL_CLIPPING_OUTPUT_PRIMITIVES',
    'GL_POLYGON_OFFSET_CLAMP',
    'GL_SPIR_V_EXTENSIONS',
    'GL_NUM_SPIR_V_EXTENSIONS',
    'GL_TEXTURE_MAX_ANISOTROPY',
    'GL_MAX_TEXTURE_MAX_ANISOTROPY',
    'GL_TRANSFORM_FEEDBACK_OVERFLOW',
    'GL_TRANSFORM_FEEDBACK_STREAM_OVERFLOW',
    'GL_MULTISAMPLE_ARB',
    'GL_SAMPLE_ALPHA_TO_COVERAGE_ARB',
    'GL_SAMPLE_ALPHA_TO_ONE_ARB',
    'GL_SAMPLE_COVERAGE_ARB',
    'GL_SAMPLE_BUFFERS_ARB',
    'GL_SAMPLES_ARB',
    'GL_SAMPLE_COVERAGE_VALUE_ARB',
    'GL_SAMPLE_COVERAGE_INVERT_ARB',
    'GL_MULTISAMPLE_BIT_ARB',
    'GL_COMPRESSED_RGB_S3TC_DXT1_EXT',
    'GL_COMPRESSED_RGBA_S3TC_DXT1_EXT',
    'GL_COMPRESSED_RGBA_S3TC_DXT3_EXT',
    'GL_COMPRESSED_RGBA_S3TC_DXT5_EXT',
    'GL_INVALID_FRAMEBUFFER_OPERATION_EXT',
    'GL_MAX_RENDERBUFFER_SIZE_EXT',
    'GL_FRAMEBUFFER_BINDING_EXT',
    'GL_RENDERBUFFER_BINDING_EXT',
    'GL_FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE_EXT',
    'GL_FRAMEBUFFER_ATTACHMENT_OBJECT_NAME_EXT',
    'GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL_EXT',
    'GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE_EXT',
    'GL_FRAMEBUFFER_ATTACHMENT_TEXTURE_3D_ZOFFSET_EXT',
    'GL_FRAMEBUFFER_COMPLETE_EXT',
    'GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT_EXT',
    'GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT_EXT',
    'GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS_EXT',
    'GL_FRAMEBUFFER_INCOMPLETE_FORMATS_EXT',
    'GL_FRAMEBUFFER_INCOMPLETE_DRAW_BUFFER_EXT',
    'GL_FRAMEBUFFER_INCOMPLETE_READ_BUFFER_EXT',
    'GL_FRAMEBUFFER_UNSUPPORTED_EXT',
    'GL_MAX_COLOR_ATTACHMENTS_EXT',
    'GL_COLOR_ATTACHMENT0_EXT',
    'GL_COLOR_ATTACHMENT1_EXT',
    'GL_COLOR_ATTACHMENT2_EXT',
    'GL_COLOR_ATTACHMENT3_EXT',
    'GL_COLOR_ATTACHMENT4_EXT',
    'GL_COLOR_ATTACHMENT5_EXT',
    'GL_COLOR_ATTACHMENT6_EXT',
    'GL_COLOR_ATTACHMENT7_EXT',
    'GL_COLOR_ATTACHMENT8_EXT',
    'GL_COLOR_ATTACHMENT9_EXT',
    'GL_COLOR_ATTACHMENT10_EXT',
    'GL_COLOR_ATTACHMENT11_EXT',
    'GL_COLOR_ATTACHMENT12_EXT',
    'GL_COLOR_ATTACHMENT13_EXT',
    'GL_COLOR_ATTACHMENT14_EXT',
    'GL_COLOR_ATTACHMENT15_EXT',
    'GL_DEPTH_ATTACHMENT_EXT',
    'GL_STENCIL_ATTACHMENT_EXT',
    'GL_FRAMEBUFFER_EXT',
    'GL_RENDERBUFFER_EXT',
    'GL_RENDERBUFFER_WIDTH_EXT',
    'GL_RENDERBUFFER_HEIGHT_EXT',
    'GL_RENDERBUFFER_INTERNAL_FORMAT_EXT',
    'GL_STENCIL_INDEX1_EXT',
    'GL_STENCIL_INDEX4_EXT',
    'GL_STENCIL_INDEX8_EXT',
    'GL_STENCIL_INDEX16_EXT',
    'GL_RENDERBUFFER_RED_SIZE_EXT',
    'GL_RENDERBUFFER_GREEN_SIZE_EXT',
    'GL_RENDERBUFFER_BLUE_SIZE_EXT',
    'GL_RENDERBUFFER_ALPHA_SIZE_EXT',
    'GL_RENDERBUFFER_DEPTH_SIZE_EXT',
    'GL_RENDERBUFFER_STENCIL_SIZE_EXT',
    'GL_UNSIGNED_INT64_ARB',
    'GL_INT64_ARB',
    'GL_INT64_VEC2_ARB',
    'GL_INT64_VEC3_ARB',
    'GL_INT64_VEC4_ARB',
    'GL_UNSIGNED_INT64_VEC2_ARB',
    'GL_UNSIGNED_INT64_VEC3_ARB',
    'GL_UNSIGNED_INT64_VEC4_ARB',
    'GL_MESH_SHADER_NV',
    'GL_TASK_SHADER_NV',
    'GL_MAX_MESH_UNIFORM_BLOCKS_NV',
    'GL_MAX_MESH_TEXTURE_IMAGE_UNITS_NV',
    'GL_MAX_MESH_IMAGE_UNIFORMS_NV',
    'GL_MAX_MESH_UNIFORM_COMPONENTS_NV',
    'GL_MAX_MESH_ATOMIC_COUNTER_BUFFERS_NV',
    'GL_MAX_MESH_ATOMIC_COUNTERS_NV',
    'GL_MAX_MESH_SHADER_STORAGE_BLOCKS_NV',
    'GL_MAX_COMBINED_MESH_UNIFORM_COMPONENTS_NV',
    'GL_MAX_TASK_UNIFORM_BLOCKS_NV',
    'GL_MAX_TASK_TEXTURE_IMAGE_UNITS_NV',
    'GL_MAX_TASK_IMAGE_UNIFORMS_NV',
    'GL_MAX_TASK_UNIFORM_COMPONENTS_NV',
    'GL_MAX_TASK_ATOMIC_COUNTER_BUFFERS_NV',
    'GL_MAX_TASK_ATOMIC_COUNTERS_NV',
    'GL_MAX_TASK_SHADER_STORAGE_BLOCKS_NV',
    'GL_MAX_COMBINED_TASK_UNIFORM_COMPONENTS_NV',
    'GL_MAX_MESH_WORK_GROUP_INVOCATIONS_NV',
    'GL_MAX_TASK_WORK_GROUP_INVOCATIONS_NV',
    'GL_MAX_MESH_TOTAL_MEMORY_SIZE_NV',
    'GL_MAX_TASK_TOTAL_MEMORY_SIZE_NV',
    'GL_MAX_MESH_OUTPUT_VERTICES_NV',
    'GL_MAX_MESH_OUTPUT_PRIMITIVES_NV',
    'GL_MAX_TASK_OUTPUT_COUNT_NV',
    'GL_MAX_DRAW_MESH_TASKS_COUNT_NV',
    'GL_MAX_MESH_VIEWS_NV',
    'GL_MESH_OUTPUT_PER_VERTEX_GRANULARITY_NV',
    'GL_MESH_OUTPUT_PER_PRIMITIVE_GRANULARITY_NV',
    'GL_MAX_MESH_WORK_GROUP_SIZE_NV',
    'GL_MAX_TASK_WORK_GROUP_SIZE_NV',
    'GL_MESH_WORK_GROUP_SIZE_NV',
    'GL_TASK_WORK_GROUP_SIZE_NV',
    'GL_MESH_VERTICES_OUT_NV',
    'GL_MESH_PRIMITIVES_OUT_NV',
    'GL_MESH_OUTPUT_TYPE_NV',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_MESH_SHADER_NV',
    'GL_UNIFORM_BLOCK_REFERENCED_BY_TASK_SHADER_NV',
    'GL_REFERENCED_BY_MESH_SHADER_NV',
    'GL_REFERENCED_BY_TASK_SHADER_NV',
    'GL_MESH_SHADER_BIT_NV',
    'GL_TASK_SHADER_BIT_NV',
    'GL_MESH_SUBROUTINE_NV',
    'GL_TASK_SUBROUTINE_NV',
    'GL_MESH_SUBROUTINE_UNIFORM_NV',
    'GL_TASK_SUBROUTINE_UNIFORM_NV',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_MESH_SHADER_NV',
    'GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_TASK_SHADER_NV',
    'glAccum',
    'glActiveShaderProgram',
    'glActiveTexture',
    'glAlphaFunc',
    'glAreTexturesResident',
    'glArrayElement',
    'glAttachShader',
    'glBegin',
    'glBeginConditionalRender',
    'glBeginQuery',
    'glBeginQueryIndexed',
    'glBeginTransformFeedback',
    'glBindAttribLocation',
    'glBindBuffer',
    'glBindBufferBase',
    'glBindBufferRange',
    'glBindBuffersBase',
    'glBindBuffersRange',
    'glBindFragDataLocation',
    'glBindFragDataLocationIndexed',
    'glBindFramebuffer',
    'glBindFramebufferEXT',
    'glBindImageTexture',
    'glBindImageTextures',
    'glBindProgramPipeline',
    'glBindRenderbuffer',
    'glBindRenderbufferEXT',
    'glBindSampler',
    'glBindSamplers',
    'glBindTexture',
    'glBindTextureUnit',
    'glBindTextures',
    'glBindTransformFeedback',
    'glBindVertexArray',
    'glBindVertexBuffer',
    'glBindVertexBuffers',
    'glBitmap',
    'glBlendColor',
    'glBlendEquation',
    'glBlendEquationSeparate',
    'glBlendEquationSeparatei',
    'glBlendEquationi',
    'glBlendFunc',
    'glBlendFuncSeparate',
    'glBlendFuncSeparatei',
    'glBlendFunci',
    'glBlitFramebuffer',
    'glBlitNamedFramebuffer',
    'glBufferData',
    'glBufferStorage',
    'glBufferSubData',
    'glCallList',
    'glCallLists',
    'glCheckFramebufferStatus',
    'glCheckFramebufferStatusEXT',
    'glCheckNamedFramebufferStatus',
    'glClampColor',
    'glClear',
    'glClearAccum',
    'glClearBufferData',
    'glClearBufferSubData',
    'glClearBufferfi',
    'glClearBufferfv',
    'glClearBufferiv',
    'glClearBufferuiv',
    'glClearColor',
    'glClearDepth',
    'glClearDepthf',
    'glClearIndex',
    'glClearNamedBufferData',
    'glClearNamedBufferSubData',
    'glClearNamedFramebufferfi',
    'glClearNamedFramebufferfv',
    'glClearNamedFramebufferiv',
    'glClearNamedFramebufferuiv',
    'glClearStencil',
    'glClearTexImage',
    'glClearTexSubImage',
    'glClientActiveTexture',
    'glClientWaitSync',
    'glClipControl',
    'glClipPlane',
    'glColor3b',
    'glColor3bv',
    'glColor3d',
    'glColor3dv',
    'glColor3f',
    'glColor3fv',
    'glColor3i',
    'glColor3iv',
    'glColor3s',
    'glColor3sv',
    'glColor3ub',
    'glColor3ubv',
    'glColor3ui',
    'glColor3uiv',
    'glColor3us',
    'glColor3usv',
    'glColor4b',
    'glColor4bv',
    'glColor4d',
    'glColor4dv',
    'glColor4f',
    'glColor4fv',
    'glColor4i',
    'glColor4iv',
    'glColor4s',
    'glColor4sv',
    'glColor4ub',
    'glColor4ubv',
    'glColor4ui',
    'glColor4uiv',
    'glColor4us',
    'glColor4usv',
    'glColorMask',
    'glColorMaski',
    'glColorMaterial',
    'glColorP3ui',
    'glColorP3uiv',
    'glColorP4ui',
    'glColorP4uiv',
    'glColorPointer',
    'glCompileShader',
    'glCompressedTexImage1D',
    'glCompressedTexImage2D',
    'glCompressedTexImage3D',
    'glCompressedTexSubImage1D',
    'glCompressedTexSubImage2D',
    'glCompressedTexSubImage3D',
    'glCompressedTextureSubImage1D',
    'glCompressedTextureSubImage2D',
    'glCompressedTextureSubImage3D',
    'glCopyBufferSubData',
    'glCopyImageSubData',
    'glCopyNamedBufferSubData',
    'glCopyPixels',
    'glCopyTexImage1D',
    'glCopyTexImage2D',
    'glCopyTexSubImage1D',
    'glCopyTexSubImage2D',
    'glCopyTexSubImage3D',
    'glCopyTextureSubImage1D',
    'glCopyTextureSubImage2D',
    'glCopyTextureSubImage3D',
    'glCreateBuffers',
    'glCreateFramebuffers',
    'glCreateProgram',
    'glCreateProgramPipelines',
    'glCreateQueries',
    'glCreateRenderbuffers',
    'glCreateSamplers',
    'glCreateShader',
    'glCreateShaderProgramv',
    'glCreateTextures',
    'glCreateTransformFeedbacks',
    'glCreateVertexArrays',
    'glCullFace',
    'glDebugMessageCallback',
    'glDebugMessageControl',
    'glDebugMessageInsert',
    'glDeleteBuffers',
    'glDeleteFramebuffers',
    'glDeleteFramebuffersEXT',
    'glDeleteLists',
    'glDeleteProgram',
    'glDeleteProgramPipelines',
    'glDeleteQueries',
    'glDeleteRenderbuffers',
    'glDeleteRenderbuffersEXT',
    'glDeleteSamplers',
    'glDeleteShader',
    'glDeleteSync',
    'glDeleteTextures',
    'glDeleteTransformFeedbacks',
    'glDeleteVertexArrays',
    'glDepthFunc',
    'glDepthMask',
    'glDepthRange',
    'glDepthRangeArrayv',
    'glDepthRangeIndexed',
    'glDepthRangef',
    'glDetachShader',
    'glDisable',
    'glDisableClientState',
    'glDisableVertexArrayAttrib',
    'glDisableVertexAttribArray',
    'glDisablei',
    'glDispatchCompute',
    'glDispatchComputeIndirect',
    'glDrawArrays',
    'glDrawArraysIndirect',
    'glDrawArraysInstanced',
    'glDrawArraysInstancedBaseInstance',
    'glDrawBuffer',
    'glDrawBuffers',
    'glDrawElements',
    'glDrawElementsBaseVertex',
    'glDrawElementsIndirect',
    'glDrawElementsInstanced',
    'glDrawElementsInstancedBaseInstance',
    'glDrawElementsInstancedBaseVertex',
    'glDrawElementsInstancedBaseVertexBaseInstance',
    'glDrawMeshTasksIndirectNV',
    'glDrawMeshTasksNV',
    'glDrawPixels',
    'glDrawRangeElements',
    'glDrawRangeElementsBaseVertex',
    'glDrawTransformFeedback',
    'glDrawTransformFeedbackInstanced',
    'glDrawTransformFeedbackStream',
    'glDrawTransformFeedbackStreamInstanced',
    'glEdgeFlag',
    'glEdgeFlagPointer',
    'glEdgeFlagv',
    'glEnable',
    'glEnableClientState',
    'glEnableVertexArrayAttrib',
    'glEnableVertexAttribArray',
    'glEnablei',
    'glEnd',
    'glEndConditionalRender',
    'glEndList',
    'glEndQuery',
    'glEndQueryIndexed',
    'glEndTransformFeedback',
    'glEvalCoord1d',
    'glEvalCoord1dv',
    'glEvalCoord1f',
    'glEvalCoord1fv',
    'glEvalCoord2d',
    'glEvalCoord2dv',
    'glEvalCoord2f',
    'glEvalCoord2fv',
    'glEvalMesh1',
    'glEvalMesh2',
    'glEvalPoint1',
    'glEvalPoint2',
    'glFeedbackBuffer',
    'glFenceSync',
    'glFinish',
    'glFlush',
    'glFlushMappedBufferRange',
    'glFlushMappedNamedBufferRange',
    'glFogCoordPointer',
    'glFogCoordd',
    'glFogCoorddv',
    'glFogCoordf',
    'glFogCoordfv',
    'glFogf',
    'glFogfv',
    'glFogi',
    'glFogiv',
    'glFramebufferParameteri',
    'glFramebufferRenderbuffer',
    'glFramebufferRenderbufferEXT',
    'glFramebufferTexture',
    'glFramebufferTexture1D',
    'glFramebufferTexture1DEXT',
    'glFramebufferTexture2D',
    'glFramebufferTexture2DEXT',
    'glFramebufferTexture3D',
    'glFramebufferTexture3DEXT',
    'glFramebufferTextureLayer',
    'glFrontFace',
    'glFrustum',
    'glGenBuffers',
    'glGenFramebuffers',
    'glGenFramebuffersEXT',
    'glGenLists',
    'glGenProgramPipelines',
    'glGenQueries',
    'glGenRenderbuffers',
    'glGenRenderbuffersEXT',
    'glGenSamplers',
    'glGenTextures',
    'glGenTransformFeedbacks',
    'glGenVertexArrays',
    'glGenerateMipmap',
    'glGenerateMipmapEXT',
    'glGenerateTextureMipmap',
    'glGetActiveAtomicCounterBufferiv',
    'glGetActiveAttrib',
    'glGetActiveSubroutineName',
    'glGetActiveSubroutineUniformName',
    'glGetActiveSubroutineUniformiv',
    'glGetActiveUniform',
    'glGetActiveUniformBlockName',
    'glGetActiveUniformBlockiv',
    'glGetActiveUniformName',
    'glGetActiveUniformsiv',
    'glGetAttachedShaders',
    'glGetAttribLocation',
    'glGetBooleani_v',
    'glGetBooleanv',
    'glGetBufferParameteri64v',
    'glGetBufferParameteriv',
    'glGetBufferPointerv',
    'glGetBufferSubData',
    'glGetClipPlane',
    'glGetCompressedTexImage',
    'glGetCompressedTextureImage',
    'glGetCompressedTextureSubImage',
    'glGetDebugMessageLog',
    'glGetDoublei_v',
    'glGetDoublev',
    'glGetError',
    'glGetFloati_v',
    'glGetFloatv',
    'glGetFragDataIndex',
    'glGetFragDataLocation',
    'glGetFramebufferAttachmentParameteriv',
    'glGetFramebufferAttachmentParameterivEXT',
    'glGetFramebufferParameteriv',
    'glGetGraphicsResetStatus',
    'glGetImageHandleARB',
    'glGetInteger64i_v',
    'glGetInteger64v',
    'glGetIntegeri_v',
    'glGetIntegerv',
    'glGetInternalformati64v',
    'glGetInternalformativ',
    'glGetLightfv',
    'glGetLightiv',
    'glGetMapdv',
    'glGetMapfv',
    'glGetMapiv',
    'glGetMaterialfv',
    'glGetMaterialiv',
    'glGetMultisamplefv',
    'glGetNamedBufferParameteri64v',
    'glGetNamedBufferParameteriv',
    'glGetNamedBufferPointerv',
    'glGetNamedBufferSubData',
    'glGetNamedFramebufferAttachmentParameteriv',
    'glGetNamedFramebufferParameteriv',
    'glGetNamedRenderbufferParameteriv',
    'glGetObjectLabel',
    'glGetObjectPtrLabel',
    'glGetPixelMapfv',
    'glGetPixelMapuiv',
    'glGetPixelMapusv',
    'glGetPointerv',
    'glGetPolygonStipple',
    'glGetProgramBinary',
    'glGetProgramInfoLog',
    'glGetProgramInterfaceiv',
    'glGetProgramPipelineInfoLog',
    'glGetProgramPipelineiv',
    'glGetProgramResourceIndex',
    'glGetProgramResourceLocation',
    'glGetProgramResourceLocationIndex',
    'glGetProgramResourceName',
    'glGetProgramResourceiv',
    'glGetProgramStageiv',
    'glGetProgramiv',
    'glGetQueryBufferObjecti64v',
    'glGetQueryBufferObjectiv',
    'glGetQueryBufferObjectui64v',
    'glGetQueryBufferObjectuiv',
    'glGetQueryIndexediv',
    'glGetQueryObjecti64v',
    'glGetQueryObjectiv',
    'glGetQueryObjectui64v',
    'glGetQueryObjectuiv',
    'glGetQueryiv',
    'glGetRenderbufferParameteriv',
    'glGetRenderbufferParameterivEXT',
    'glGetSamplerParameterIiv',
    'glGetSamplerParameterIuiv',
    'glGetSamplerParameterfv',
    'glGetSamplerParameteriv',
    'glGetShaderInfoLog',
    'glGetShaderPrecisionFormat',
    'glGetShaderSource',
    'glGetShaderiv',
    'glGetString',
    'glGetStringi',
    'glGetSubroutineIndex',
    'glGetSubroutineUniformLocation',
    'glGetSynciv',
    'glGetTexEnvfv',
    'glGetTexEnviv',
    'glGetTexGendv',
    'glGetTexGenfv',
    'glGetTexGeniv',
    'glGetTexImage',
    'glGetTexLevelParameterfv',
    'glGetTexLevelParameteriv',
    'glGetTexParameterIiv',
    'glGetTexParameterIuiv',
    'glGetTexParameterfv',
    'glGetTexParameteriv',
    'glGetTextureHandleARB',
    'glGetTextureImage',
    'glGetTextureLevelParameterfv',
    'glGetTextureLevelParameteriv',
    'glGetTextureParameterIiv',
    'glGetTextureParameterIuiv',
    'glGetTextureParameterfv',
    'glGetTextureParameteriv',
    'glGetTextureSamplerHandleARB',
    'glGetTextureSubImage',
    'glGetTransformFeedbackVarying',
    'glGetTransformFeedbacki64_v',
    'glGetTransformFeedbacki_v',
    'glGetTransformFeedbackiv',
    'glGetUniformBlockIndex',
    'glGetUniformIndices',
    'glGetUniformLocation',
    'glGetUniformSubroutineuiv',
    'glGetUniformdv',
    'glGetUniformfv',
    'glGetUniformi64vARB',
    'glGetUniformiv',
    'glGetUniformui64vARB',
    'glGetUniformuiv',
    'glGetVertexArrayIndexed64iv',
    'glGetVertexArrayIndexediv',
    'glGetVertexArrayiv',
    'glGetVertexAttribIiv',
    'glGetVertexAttribIuiv',
    'glGetVertexAttribLdv',
    'glGetVertexAttribLui64vARB',
    'glGetVertexAttribPointerv',
    'glGetVertexAttribdv',
    'glGetVertexAttribfv',
    'glGetVertexAttribiv',
    'glGetnColorTable',
    'glGetnCompressedTexImage',
    'glGetnConvolutionFilter',
    'glGetnHistogram',
    'glGetnMapdv',
    'glGetnMapfv',
    'glGetnMapiv',
    'glGetnMinmax',
    'glGetnPixelMapfv',
    'glGetnPixelMapuiv',
    'glGetnPixelMapusv',
    'glGetnPolygonStipple',
    'glGetnSeparableFilter',
    'glGetnTexImage',
    'glGetnUniformdv',
    'glGetnUniformfv',
    'glGetnUniformi64vARB',
    'glGetnUniformiv',
    'glGetnUniformui64vARB',
    'glGetnUniformuiv',
    'glHint',
    'glIndexMask',
    'glIndexPointer',
    'glIndexd',
    'glIndexdv',
    'glIndexf',
    'glIndexfv',
    'glIndexi',
    'glIndexiv',
    'glIndexs',
    'glIndexsv',
    'glIndexub',
    'glIndexubv',
    'glInitNames',
    'glInterleavedArrays',
    'glInvalidateBufferData',
    'glInvalidateBufferSubData',
    'glInvalidateFramebuffer',
    'glInvalidateNamedFramebufferData',
    'glInvalidateNamedFramebufferSubData',
    'glInvalidateSubFramebuffer',
    'glInvalidateTexImage',
    'glInvalidateTexSubImage',
    'glIsBuffer',
    'glIsEnabled',
    'glIsEnabledi',
    'glIsFramebuffer',
    'glIsFramebufferEXT',
    'glIsImageHandleResidentARB',
    'glIsList',
    'glIsProgram',
    'glIsProgramPipeline',
    'glIsQuery',
    'glIsRenderbuffer',
    'glIsRenderbufferEXT',
    'glIsSampler',
    'glIsShader',
    'glIsSync',
    'glIsTexture',
    'glIsTextureHandleResidentARB',
    'glIsTransformFeedback',
    'glIsVertexArray',
    'glLightModelf',
    'glLightModelfv',
    'glLightModeli',
    'glLightModeliv',
    'glLightf',
    'glLightfv',
    'glLighti',
    'glLightiv',
    'glLineStipple',
    'glLineWidth',
    'glLinkProgram',
    'glListBase',
    'glLoadIdentity',
    'glLoadMatrixd',
    'glLoadMatrixf',
    'glLoadName',
    'glLoadTransposeMatrixd',
    'glLoadTransposeMatrixf',
    'glLogicOp',
    'glMakeImageHandleNonResidentARB',
    'glMakeImageHandleResidentARB',
    'glMakeTextureHandleNonResidentARB',
    'glMakeTextureHandleResidentARB',
    'glMap1d',
    'glMap1f',
    'glMap2d',
    'glMap2f',
    'glMapBuffer',
    'glMapBufferRange',
    'glMapGrid1d',
    'glMapGrid1f',
    'glMapGrid2d',
    'glMapGrid2f',
    'glMapNamedBuffer',
    'glMapNamedBufferRange',
    'glMaterialf',
    'glMaterialfv',
    'glMateriali',
    'glMaterialiv',
    'glMatrixMode',
    'glMemoryBarrier',
    'glMemoryBarrierByRegion',
    'glMinSampleShading',
    'glMultMatrixd',
    'glMultMatrixf',
    'glMultTransposeMatrixd',
    'glMultTransposeMatrixf',
    'glMultiDrawArrays',
    'glMultiDrawArraysIndirect',
    'glMultiDrawArraysIndirectCount',
    'glMultiDrawElements',
    'glMultiDrawElementsBaseVertex',
    'glMultiDrawElementsIndirect',
    'glMultiDrawElementsIndirectCount',
    'glMultiDrawMeshTasksIndirectCountNV',
    'glMultiDrawMeshTasksIndirectNV',
    'glMultiTexCoord1d',
    'glMultiTexCoord1dv',
    'glMultiTexCoord1f',
    'glMultiTexCoord1fv',
    'glMultiTexCoord1i',
    'glMultiTexCoord1iv',
    'glMultiTexCoord1s',
    'glMultiTexCoord1sv',
    'glMultiTexCoord2d',
    'glMultiTexCoord2dv',
    'glMultiTexCoord2f',
    'glMultiTexCoord2fv',
    'glMultiTexCoord2i',
    'glMultiTexCoord2iv',
    'glMultiTexCoord2s',
    'glMultiTexCoord2sv',
    'glMultiTexCoord3d',
    'glMultiTexCoord3dv',
    'glMultiTexCoord3f',
    'glMultiTexCoord3fv',
    'glMultiTexCoord3i',
    'glMultiTexCoord3iv',
    'glMultiTexCoord3s',
    'glMultiTexCoord3sv',
    'glMultiTexCoord4d',
    'glMultiTexCoord4dv',
    'glMultiTexCoord4f',
    'glMultiTexCoord4fv',
    'glMultiTexCoord4i',
    'glMultiTexCoord4iv',
    'glMultiTexCoord4s',
    'glMultiTexCoord4sv',
    'glMultiTexCoordP1ui',
    'glMultiTexCoordP1uiv',
    'glMultiTexCoordP2ui',
    'glMultiTexCoordP2uiv',
    'glMultiTexCoordP3ui',
    'glMultiTexCoordP3uiv',
    'glMultiTexCoordP4ui',
    'glMultiTexCoordP4uiv',
    'glNamedBufferData',
    'glNamedBufferStorage',
    'glNamedBufferSubData',
    'glNamedFramebufferDrawBuffer',
    'glNamedFramebufferDrawBuffers',
    'glNamedFramebufferParameteri',
    'glNamedFramebufferReadBuffer',
    'glNamedFramebufferRenderbuffer',
    'glNamedFramebufferTexture',
    'glNamedFramebufferTextureLayer',
    'glNamedRenderbufferStorage',
    'glNamedRenderbufferStorageMultisample',
    'glNewList',
    'glNormal3b',
    'glNormal3bv',
    'glNormal3d',
    'glNormal3dv',
    'glNormal3f',
    'glNormal3fv',
    'glNormal3i',
    'glNormal3iv',
    'glNormal3s',
    'glNormal3sv',
    'glNormalP3ui',
    'glNormalP3uiv',
    'glNormalPointer',
    'glObjectLabel',
    'glObjectPtrLabel',
    'glOrtho',
    'glPassThrough',
    'glPatchParameterfv',
    'glPatchParameteri',
    'glPauseTransformFeedback',
    'glPixelMapfv',
    'glPixelMapuiv',
    'glPixelMapusv',
    'glPixelStoref',
    'glPixelStorei',
    'glPixelTransferf',
    'glPixelTransferi',
    'glPixelZoom',
    'glPointParameterf',
    'glPointParameterfv',
    'glPointParameteri',
    'glPointParameteriv',
    'glPointSize',
    'glPolygonMode',
    'glPolygonOffset',
    'glPolygonOffsetClamp',
    'glPolygonStipple',
    'glPopAttrib',
    'glPopClientAttrib',
    'glPopDebugGroup',
    'glPopMatrix',
    'glPopName',
    'glPrimitiveRestartIndex',
    'glPrioritizeTextures',
    'glProgramBinary',
    'glProgramParameteri',
    'glProgramUniform1d',
    'glProgramUniform1dv',
    'glProgramUniform1f',
    'glProgramUniform1fv',
    'glProgramUniform1i',
    'glProgramUniform1i64ARB',
    'glProgramUniform1i64vARB',
    'glProgramUniform1iv',
    'glProgramUniform1ui',
    'glProgramUniform1ui64ARB',
    'glProgramUniform1ui64vARB',
    'glProgramUniform1uiv',
    'glProgramUniform2d',
    'glProgramUniform2dv',
    'glProgramUniform2f',
    'glProgramUniform2fv',
    'glProgramUniform2i',
    'glProgramUniform2i64ARB',
    'glProgramUniform2i64vARB',
    'glProgramUniform2iv',
    'glProgramUniform2ui',
    'glProgramUniform2ui64ARB',
    'glProgramUniform2ui64vARB',
    'glProgramUniform2uiv',
    'glProgramUniform3d',
    'glProgramUniform3dv',
    'glProgramUniform3f',
    'glProgramUniform3fv',
    'glProgramUniform3i',
    'glProgramUniform3i64ARB',
    'glProgramUniform3i64vARB',
    'glProgramUniform3iv',
    'glProgramUniform3ui',
    'glProgramUniform3ui64ARB',
    'glProgramUniform3ui64vARB',
    'glProgramUniform3uiv',
    'glProgramUniform4d',
    'glProgramUniform4dv',
    'glProgramUniform4f',
    'glProgramUniform4fv',
    'glProgramUniform4i',
    'glProgramUniform4i64ARB',
    'glProgramUniform4i64vARB',
    'glProgramUniform4iv',
    'glProgramUniform4ui',
    'glProgramUniform4ui64ARB',
    'glProgramUniform4ui64vARB',
    'glProgramUniform4uiv',
    'glProgramUniformHandleui64ARB',
    'glProgramUniformHandleui64vARB',
    'glProgramUniformMatrix2dv',
    'glProgramUniformMatrix2fv',
    'glProgramUniformMatrix2x3dv',
    'glProgramUniformMatrix2x3fv',
    'glProgramUniformMatrix2x4dv',
    'glProgramUniformMatrix2x4fv',
    'glProgramUniformMatrix3dv',
    'glProgramUniformMatrix3fv',
    'glProgramUniformMatrix3x2dv',
    'glProgramUniformMatrix3x2fv',
    'glProgramUniformMatrix3x4dv',
    'glProgramUniformMatrix3x4fv',
    'glProgramUniformMatrix4dv',
    'glProgramUniformMatrix4fv',
    'glProgramUniformMatrix4x2dv',
    'glProgramUniformMatrix4x2fv',
    'glProgramUniformMatrix4x3dv',
    'glProgramUniformMatrix4x3fv',
    'glProvokingVertex',
    'glPushAttrib',
    'glPushClientAttrib',
    'glPushDebugGroup',
    'glPushMatrix',
    'glPushName',
    'glQueryCounter',
    'glRasterPos2d',
    'glRasterPos2dv',
    'glRasterPos2f',
    'glRasterPos2fv',
    'glRasterPos2i',
    'glRasterPos2iv',
    'glRasterPos2s',
    'glRasterPos2sv',
    'glRasterPos3d',
    'glRasterPos3dv',
    'glRasterPos3f',
    'glRasterPos3fv',
    'glRasterPos3i',
    'glRasterPos3iv',
    'glRasterPos3s',
    'glRasterPos3sv',
    'glRasterPos4d',
    'glRasterPos4dv',
    'glRasterPos4f',
    'glRasterPos4fv',
    'glRasterPos4i',
    'glRasterPos4iv',
    'glRasterPos4s',
    'glRasterPos4sv',
    'glReadBuffer',
    'glReadPixels',
    'glReadnPixels',
    'glRectd',
    'glRectdv',
    'glRectf',
    'glRectfv',
    'glRecti',
    'glRectiv',
    'glRects',
    'glRectsv',
    'glReleaseShaderCompiler',
    'glRenderMode',
    'glRenderbufferStorage',
    'glRenderbufferStorageEXT',
    'glRenderbufferStorageMultisample',
    'glResumeTransformFeedback',
    'glRotated',
    'glRotatef',
    'glSampleCoverage',
    'glSampleCoverageARB',
    'glSampleMaski',
    'glSamplerParameterIiv',
    'glSamplerParameterIuiv',
    'glSamplerParameterf',
    'glSamplerParameterfv',
    'glSamplerParameteri',
    'glSamplerParameteriv',
    'glScaled',
    'glScalef',
    'glScissor',
    'glScissorArrayv',
    'glScissorIndexed',
    'glScissorIndexedv',
    'glSecondaryColor3b',
    'glSecondaryColor3bv',
    'glSecondaryColor3d',
    'glSecondaryColor3dv',
    'glSecondaryColor3f',
    'glSecondaryColor3fv',
    'glSecondaryColor3i',
    'glSecondaryColor3iv',
    'glSecondaryColor3s',
    'glSecondaryColor3sv',
    'glSecondaryColor3ub',
    'glSecondaryColor3ubv',
    'glSecondaryColor3ui',
    'glSecondaryColor3uiv',
    'glSecondaryColor3us',
    'glSecondaryColor3usv',
    'glSecondaryColorP3ui',
    'glSecondaryColorP3uiv',
    'glSecondaryColorPointer',
    'glSelectBuffer',
    'glShadeModel',
    'glShaderBinary',
    'glShaderSource',
    'glShaderStorageBlockBinding',
    'glSpecializeShader',
    'glStencilFunc',
    'glStencilFuncSeparate',
    'glStencilMask',
    'glStencilMaskSeparate',
    'glStencilOp',
    'glStencilOpSeparate',
    'glTexBuffer',
    'glTexBufferRange',
    'glTexCoord1d',
    'glTexCoord1dv',
    'glTexCoord1f',
    'glTexCoord1fv',
    'glTexCoord1i',
    'glTexCoord1iv',
    'glTexCoord1s',
    'glTexCoord1sv',
    'glTexCoord2d',
    'glTexCoord2dv',
    'glTexCoord2f',
    'glTexCoord2fv',
    'glTexCoord2i',
    'glTexCoord2iv',
    'glTexCoord2s',
    'glTexCoord2sv',
    'glTexCoord3d',
    'glTexCoord3dv',
    'glTexCoord3f',
    'glTexCoord3fv',
    'glTexCoord3i',
    'glTexCoord3iv',
    'glTexCoord3s',
    'glTexCoord3sv',
    'glTexCoord4d',
    'glTexCoord4dv',
    'glTexCoord4f',
    'glTexCoord4fv',
    'glTexCoord4i',
    'glTexCoord4iv',
    'glTexCoord4s',
    'glTexCoord4sv',
    'glTexCoordP1ui',
    'glTexCoordP1uiv',
    'glTexCoordP2ui',
    'glTexCoordP2uiv',
    'glTexCoordP3ui',
    'glTexCoordP3uiv',
    'glTexCoordP4ui',
    'glTexCoordP4uiv',
    'glTexCoordPointer',
    'glTexEnvf',
    'glTexEnvfv',
    'glTexEnvi',
    'glTexEnviv',
    'glTexGend',
    'glTexGendv',
    'glTexGenf',
    'glTexGenfv',
    'glTexGeni',
    'glTexGeniv',
    'glTexImage1D',
    'glTexImage2D',
    'glTexImage2DMultisample',
    'glTexImage3D',
    'glTexImage3DMultisample',
    'glTexParameterIiv',
    'glTexParameterIuiv',
    'glTexParameterf',
    'glTexParameterfv',
    'glTexParameteri',
    'glTexParameteriv',
    'glTexStorage1D',
    'glTexStorage2D',
    'glTexStorage2DMultisample',
    'glTexStorage3D',
    'glTexStorage3DMultisample',
    'glTexSubImage1D',
    'glTexSubImage2D',
    'glTexSubImage3D',
    'glTextureBarrier',
    'glTextureBuffer',
    'glTextureBufferRange',
    'glTextureParameterIiv',
    'glTextureParameterIuiv',
    'glTextureParameterf',
    'glTextureParameterfv',
    'glTextureParameteri',
    'glTextureParameteriv',
    'glTextureStorage1D',
    'glTextureStorage2D',
    'glTextureStorage2DMultisample',
    'glTextureStorage3D',
    'glTextureStorage3DMultisample',
    'glTextureSubImage1D',
    'glTextureSubImage2D',
    'glTextureSubImage3D',
    'glTextureView',
    'glTransformFeedbackBufferBase',
    'glTransformFeedbackBufferRange',
    'glTransformFeedbackVaryings',
    'glTranslated',
    'glTranslatef',
    'glUniform1d',
    'glUniform1dv',
    'glUniform1f',
    'glUniform1fv',
    'glUniform1i',
    'glUniform1i64ARB',
    'glUniform1i64vARB',
    'glUniform1iv',
    'glUniform1ui',
    'glUniform1ui64ARB',
    'glUniform1ui64vARB',
    'glUniform1uiv',
    'glUniform2d',
    'glUniform2dv',
    'glUniform2f',
    'glUniform2fv',
    'glUniform2i',
    'glUniform2i64ARB',
    'glUniform2i64vARB',
    'glUniform2iv',
    'glUniform2ui',
    'glUniform2ui64ARB',
    'glUniform2ui64vARB',
    'glUniform2uiv',
    'glUniform3d',
    'glUniform3dv',
    'glUniform3f',
    'glUniform3fv',
    'glUniform3i',
    'glUniform3i64ARB',
    'glUniform3i64vARB',
    'glUniform3iv',
    'glUniform3ui',
    'glUniform3ui64ARB',
    'glUniform3ui64vARB',
    'glUniform3uiv',
    'glUniform4d',
    'glUniform4dv',
    'glUniform4f',
    'glUniform4fv',
    'glUniform4i',
    'glUniform4i64ARB',
    'glUniform4i64vARB',
    'glUniform4iv',
    'glUniform4ui',
    'glUniform4ui64ARB',
    'glUniform4ui64vARB',
    'glUniform4uiv',
    'glUniformBlockBinding',
    'glUniformHandleui64ARB',
    'glUniformHandleui64vARB',
    'glUniformMatrix2dv',
    'glUniformMatrix2fv',
    'glUniformMatrix2x3dv',
    'glUniformMatrix2x3fv',
    'glUniformMatrix2x4dv',
    'glUniformMatrix2x4fv',
    'glUniformMatrix3dv',
    'glUniformMatrix3fv',
    'glUniformMatrix3x2dv',
    'glUniformMatrix3x2fv',
    'glUniformMatrix3x4dv',
    'glUniformMatrix3x4fv',
    'glUniformMatrix4dv',
    'glUniformMatrix4fv',
    'glUniformMatrix4x2dv',
    'glUniformMatrix4x2fv',
    'glUniformMatrix4x3dv',
    'glUniformMatrix4x3fv',
    'glUniformSubroutinesuiv',
    'glUnmapBuffer',
    'glUnmapNamedBuffer',
    'glUseProgram',
    'glUseProgramStages',
    'glValidateProgram',
    'glValidateProgramPipeline',
    'glVertex2d',
    'glVertex2dv',
    'glVertex2f',
    'glVertex2fv',
    'glVertex2i',
    'glVertex2iv',
    'glVertex2s',
    'glVertex2sv',
    'glVertex3d',
    'glVertex3dv',
    'glVertex3f',
    'glVertex3fv',
    'glVertex3i',
    'glVertex3iv',
    'glVertex3s',
    'glVertex3sv',
    'glVertex4d',
    'glVertex4dv',
    'glVertex4f',
    'glVertex4fv',
    'glVertex4i',
    'glVertex4iv',
    'glVertex4s',
    'glVertex4sv',
    'glVertexArrayAttribBinding',
    'glVertexArrayAttribFormat',
    'glVertexArrayAttribIFormat',
    'glVertexArrayAttribLFormat',
    'glVertexArrayBindingDivisor',
    'glVertexArrayElementBuffer',
    'glVertexArrayVertexBuffer',
    'glVertexArrayVertexBuffers',
    'glVertexAttrib1d',
    'glVertexAttrib1dv',
    'glVertexAttrib1f',
    'glVertexAttrib1fv',
    'glVertexAttrib1s',
    'glVertexAttrib1sv',
    'glVertexAttrib2d',
    'glVertexAttrib2dv',
    'glVertexAttrib2f',
    'glVertexAttrib2fv',
    'glVertexAttrib2s',
    'glVertexAttrib2sv',
    'glVertexAttrib3d',
    'glVertexAttrib3dv',
    'glVertexAttrib3f',
    'glVertexAttrib3fv',
    'glVertexAttrib3s',
    'glVertexAttrib3sv',
    'glVertexAttrib4Nbv',
    'glVertexAttrib4Niv',
    'glVertexAttrib4Nsv',
    'glVertexAttrib4Nub',
    'glVertexAttrib4Nubv',
    'glVertexAttrib4Nuiv',
    'glVertexAttrib4Nusv',
    'glVertexAttrib4bv',
    'glVertexAttrib4d',
    'glVertexAttrib4dv',
    'glVertexAttrib4f',
    'glVertexAttrib4fv',
    'glVertexAttrib4iv',
    'glVertexAttrib4s',
    'glVertexAttrib4sv',
    'glVertexAttrib4ubv',
    'glVertexAttrib4uiv',
    'glVertexAttrib4usv',
    'glVertexAttribBinding',
    'glVertexAttribDivisor',
    'glVertexAttribFormat',
    'glVertexAttribI1i',
    'glVertexAttribI1iv',
    'glVertexAttribI1ui',
    'glVertexAttribI1uiv',
    'glVertexAttribI2i',
    'glVertexAttribI2iv',
    'glVertexAttribI2ui',
    'glVertexAttribI2uiv',
    'glVertexAttribI3i',
    'glVertexAttribI3iv',
    'glVertexAttribI3ui',
    'glVertexAttribI3uiv',
    'glVertexAttribI4bv',
    'glVertexAttribI4i',
    'glVertexAttribI4iv',
    'glVertexAttribI4sv',
    'glVertexAttribI4ubv',
    'glVertexAttribI4ui',
    'glVertexAttribI4uiv',
    'glVertexAttribI4usv',
    'glVertexAttribIFormat',
    'glVertexAttribIPointer',
    'glVertexAttribL1d',
    'glVertexAttribL1dv',
    'glVertexAttribL1ui64ARB',
    'glVertexAttribL1ui64vARB',
    'glVertexAttribL2d',
    'glVertexAttribL2dv',
    'glVertexAttribL3d',
    'glVertexAttribL3dv',
    'glVertexAttribL4d',
    'glVertexAttribL4dv',
    'glVertexAttribLFormat',
    'glVertexAttribLPointer',
    'glVertexAttribP1ui',
    'glVertexAttribP1uiv',
    'glVertexAttribP2ui',
    'glVertexAttribP2uiv',
    'glVertexAttribP3ui',
    'glVertexAttribP3uiv',
    'glVertexAttribP4ui',
    'glVertexAttribP4uiv',
    'glVertexAttribPointer',
    'glVertexBindingDivisor',
    'glVertexP2ui',
    'glVertexP2uiv',
    'glVertexP3ui',
    'glVertexP3uiv',
    'glVertexP4ui',
    'glVertexP4uiv',
    'glVertexPointer',
    'glViewport',
    'glViewportArrayv',
    'glViewportIndexedf',
    'glViewportIndexedfv',
    'glWaitSync',
    'glWindowPos2d',
    'glWindowPos2dv',
    'glWindowPos2f',
    'glWindowPos2fv',
    'glWindowPos2i',
    'glWindowPos2iv',
    'glWindowPos2s',
    'glWindowPos2sv',
    'glWindowPos3d',
    'glWindowPos3dv',
    'glWindowPos3f',
    'glWindowPos3fv',
    'glWindowPos3i',
    'glWindowPos3iv',
    'glWindowPos3s',
    'glWindowPos3sv',
]
